((e,t)=>{"object"==typeof module&&"object"==typeof module.exports?module.exports=e.document?t(e,!0):function(e){if(e.document)return t(e);throw new Error("jQuery requires a window with a document")}:t(e)})("undefined"!=typeof window?window:this,function(C,q){function y(e){return"function"==typeof e&&"number"!=typeof e.nodeType&&"function"!=typeof e.item}function M(e){return null!=e&&e===e.window}var t=[],_=Object.getPrototypeOf,s=t.slice,$=t.flat?function(e){return t.flat.call(e)}:function(e){return t.concat.apply([],e)},B=t.push,b=t.indexOf,R={},F=R.toString,z=R.hasOwnProperty,W=z.toString,U=W.call(Object),m={},T=C.document,X={type:!0,src:!0,nonce:!0,noModule:!0};function G(e,t,n){var r,o,i=(n=n||T).createElement("script");if(i.text=e,t)for(r in X)(o=t[r]||t.getAttribute&&t.getAttribute(r))&&i.setAttribute(r,o);n.head.appendChild(i).parentNode.removeChild(i)}function Y(e){return null==e?e+"":"object"==typeof e||"function"==typeof e?R[F.call(e)]||"object":typeof e}var e="3.7.1",V=/HTML$/i,k=function(e,t){return new k.fn.init(e,t)};function K(e){var t=!!e&&"length"in e&&e.length,n=Y(e);return!y(e)&&!M(e)&&("array"===n||0===t||"number"==typeof t&&0<t&&t-1 in e)}function x(e,t){return e.nodeName&&e.nodeName.toLowerCase()===t.toLowerCase()}k.fn=k.prototype={jquery:e,constructor:k,length:0,toArray:function(){return s.call(this)},get:function(e){return null==e?s.call(this):e<0?this[e+this.length]:this[e]},pushStack:function(e){e=k.merge(this.constructor(),e);return e.prevObject=this,e},each:function(e){return k.each(this,e)},map:function(n){return this.pushStack(k.map(this,function(e,t){return n.call(e,t,e)}))},slice:function(){return this.pushStack(s.apply(this,arguments))},first:function(){return this.eq(0)},last:function(){return this.eq(-1)},even:function(){return this.pushStack(k.grep(this,function(e,t){return(t+1)%2}))},odd:function(){return this.pushStack(k.grep(this,function(e,t){return t%2}))},eq:function(e){var t=this.length,e=+e+(e<0?t:0);return this.pushStack(0<=e&&e<t?[this[e]]:[])},end:function(){return this.prevObject||this.constructor()},push:B,sort:t.sort,splice:t.splice},k.extend=k.fn.extend=function(){var e,t,n,r,o,i=arguments[0]||{},a=1,s=arguments.length,l=!1;for("boolean"==typeof i&&(l=i,i=arguments[a]||{},a++),"object"==typeof i||y(i)||(i={}),a===s&&(i=this,a--);a<s;a++)if(null!=(e=arguments[a]))for(t in e)n=e[t],"__proto__"!==t&&i!==n&&(l&&n&&(k.isPlainObject(n)||(r=Array.isArray(n)))?(o=i[t],o=r&&!Array.isArray(o)?[]:r||k.isPlainObject(o)?o:{},r=!1,i[t]=k.extend(l,o,n)):void 0!==n&&(i[t]=n));return i},k.extend({expando:"jQuery"+(e+Math.random()).replace(/\D/g,""),isReady:!0,error:function(e){throw new Error(e)},noop:function(){},isPlainObject:function(e){return!(!e||"[object Object]"!==F.call(e)||(e=_(e))&&("function"!=typeof(e=z.call(e,"constructor")&&e.constructor)||W.call(e)!==U))},isEmptyObject:function(e){for(var t in e)return!1;return!0},globalEval:function(e,t,n){G(e,{nonce:t&&t.nonce},n)},each:function(e,t){var n,r=0;if(K(e))for(n=e.length;r<n&&!1!==t.call(e[r],r,e[r]);r++);else for(r in e)if(!1===t.call(e[r],r,e[r]))break;return e},text:function(e){var t,n="",r=0,o=e.nodeType;if(!o)for(;t=e[r++];)n+=k.text(t);return 1===o||11===o?e.textContent:9===o?e.documentElement.textContent:3===o||4===o?e.nodeValue:n},makeArray:function(e,t){t=t||[];return null!=e&&(K(Object(e))?k.merge(t,"string"==typeof e?[e]:e):B.call(t,e)),t},inArray:function(e,t,n){return null==t?-1:b.call(t,e,n)},isXMLDoc:function(e){var t=e&&e.namespaceURI,e=e&&(e.ownerDocument||e).documentElement;return!V.test(t||e&&e.nodeName||"HTML")},merge:function(e,t){for(var n=+t.length,r=0,o=e.length;r<n;r++)e[o++]=t[r];return e.length=o,e},grep:function(e,t,n){for(var r=[],o=0,i=e.length,a=!n;o<i;o++)!t(e[o],o)!=a&&r.push(e[o]);return r},map:function(e,t,n){var r,o,i=0,a=[];if(K(e))for(r=e.length;i<r;i++)null!=(o=t(e[i],i,n))&&a.push(o);else for(i in e)null!=(o=t(e[i],i,n))&&a.push(o);return $(a)},guid:1,support:m}),"function"==typeof Symbol&&(k.fn[Symbol.iterator]=t[Symbol.iterator]),k.each("Boolean Number String Function Array Date RegExp Object Error Symbol".split(" "),function(e,t){R["[object "+t+"]"]=t.toLowerCase()});var Q=t.pop,Z=t.sort,J=t.splice,n="[\\x20\\t\\r\\n\\f]",ee=new RegExp("^"+n+"+|((?:^|[^\\\\])(?:\\\\.)*)"+n+"+$","g"),te=(k.contains=function(e,t){t=t&&t.parentNode;return e===t||!(!t||1!==t.nodeType||!(e.contains?e.contains(t):e.compareDocumentPosition&&16&e.compareDocumentPosition(t)))},/([\0-\x1f\x7f]|^-?\d)|^-$|[^\x80-\uFFFF\w-]/g);function ne(e,t){return t?"\0"===e?"�":e.slice(0,-1)+"\\"+e.charCodeAt(e.length-1).toString(16)+" ":"\\"+e}k.escapeSelector=function(e){return(e+"").replace(te,ne)};var re,w,oe,ie,ae,S,r,E,p,se,o=T,le=B,j=le,A=k.expando,D=0,ce=0,ue=De(),fe=De(),pe=De(),de=De(),he=function(e,t){return e===t&&(ae=!0),0},me="checked|selected|async|autofocus|autoplay|controls|defer|disabled|hidden|ismap|loop|multiple|open|readonly|required|scoped",e="(?:\\\\[\\da-fA-F]{1,6}"+n+"?|\\\\[^\\r\\n\\f]|[\\w-]|[^\0-\\x7f])+",i="\\["+n+"*("+e+")(?:"+n+"*([*^$|!~]?=)"+n+"*(?:'((?:\\\\.|[^\\\\'])*)'|\"((?:\\\\.|[^\\\\\"])*)\"|("+e+"))|)"+n+"*\\]",a=":("+e+")(?:\\((('((?:\\\\.|[^\\\\'])*)'|\"((?:\\\\.|[^\\\\\"])*)\")|((?:\\\\.|[^\\\\()[\\]]|"+i+")*)|.*)\\)|)",ge=new RegExp(n+"+","g"),ve=new RegExp("^"+n+"*,"+n+"*"),ye=new RegExp("^"+n+"*([>+~]|"+n+")"+n+"*"),xe=new RegExp(n+"|>"),be=new RegExp(a),we=new RegExp("^"+e+"$"),Ce={ID:new RegExp("^#("+e+")"),CLASS:new RegExp("^\\.("+e+")"),TAG:new RegExp("^("+e+"|[*])"),ATTR:new RegExp("^"+i),PSEUDO:new RegExp("^"+a),CHILD:new RegExp("^:(only|first|last|nth|nth-last)-(child|of-type)(?:\\("+n+"*(even|odd|(([+-]|)(\\d*)n|)"+n+"*(?:([+-]|)"+n+"*(\\d+)|))"+n+"*\\)|)","i"),bool:new RegExp("^(?:"+me+")$","i"),needsContext:new RegExp("^"+n+"*[>+~]|:(even|odd|eq|gt|lt|nth|first|last)(?:\\("+n+"*((?:-\\d)?\\d*)"+n+"*\\)|)(?=[^-]|$)","i")},Te=/^(?:input|select|textarea|button)$/i,ke=/^h\d$/i,Se=/^(?:#([\w-]+)|(\w+)|\.([\w-]+))$/,Ee=/[+~]/,f=new RegExp("\\\\[\\da-fA-F]{1,6}"+n+"?|\\\\([^\\r\\n\\f])","g"),d=function(e,t){e="0x"+e.slice(1)-65536;return t||(e<0?String.fromCharCode(65536+e):String.fromCharCode(e>>10|55296,1023&e|56320))},je=function(){Pe()},Ae=_e(function(e){return!0===e.disabled&&x(e,"fieldset")},{dir:"parentNode",next:"legend"});try{j.apply(t=s.call(o.childNodes),o.childNodes),t[o.childNodes.length].nodeType}catch(re){j={apply:function(e,t){le.apply(e,s.call(t))},call:function(e){le.apply(e,s.call(arguments,1))}}}function N(e,t,n,r){var o,i,a,s,l,c,u=t&&t.ownerDocument,f=t?t.nodeType:9;if(n=n||[],"string"!=typeof e||!e||1!==f&&9!==f&&11!==f)return n;if(!r&&(Pe(t),t=t||S,E)){if(11!==f&&(s=Se.exec(e)))if(o=s[1]){if(9===f){if(!(c=t.getElementById(o)))return n;if(c.id===o)return j.call(n,c),n}else if(u&&(c=u.getElementById(o))&&N.contains(t,c)&&c.id===o)return j.call(n,c),n}else{if(s[2])return j.apply(n,t.getElementsByTagName(e)),n;if((o=s[3])&&t.getElementsByClassName)return j.apply(n,t.getElementsByClassName(o)),n}if(!(de[e+" "]||p&&p.test(e))){if(c=e,u=t,1===f&&(xe.test(e)||ye.test(e))){for((u=Ee.test(e)&&Le(t.parentNode)||t)==t&&m.scope||((a=t.getAttribute("id"))?a=k.escapeSelector(a):t.setAttribute("id",a=A)),i=(l=qe(e)).length;i--;)l[i]=(a?"#"+a:":scope")+" "+Me(l[i]);c=l.join(",")}try{return j.apply(n,u.querySelectorAll(c)),n}catch(t){de(e,!0)}finally{a===A&&t.removeAttribute("id")}}}return ze(e.replace(ee,"$1"),t,n,r)}function De(){var r=[];return function e(t,n){return r.push(t+" ")>w.cacheLength&&delete e[r.shift()],e[t+" "]=n}}function l(e){return e[A]=!0,e}function Ne(e){var t=S.createElement("fieldset");try{return!!e(t)}catch(e){return!1}finally{t.parentNode&&t.parentNode.removeChild(t)}}function Ie(t){return function(e){return"form"in e?e.parentNode&&!1===e.disabled?"label"in e?"label"in e.parentNode?e.parentNode.disabled===t:e.disabled===t:e.isDisabled===t||e.isDisabled!==!t&&Ae(e)===t:e.disabled===t:"label"in e&&e.disabled===t}}function Oe(a){return l(function(i){return i=+i,l(function(e,t){for(var n,r=a([],e.length,i),o=r.length;o--;)e[n=r[o]]&&(e[n]=!(t[n]=e[n]))})})}function Le(e){return e&&void 0!==e.getElementsByTagName&&e}function Pe(e){var e=e?e.ownerDocument||e:o;return e!=S&&9===e.nodeType&&e.documentElement&&(r=(S=e).documentElement,E=!k.isXMLDoc(S),se=r.matches||r.webkitMatchesSelector||r.msMatchesSelector,r.msMatchesSelector&&o!=S&&(e=S.defaultView)&&e.top!==e&&e.addEventListener("unload",je),m.getById=Ne(function(e){return r.appendChild(e).id=k.expando,!S.getElementsByName||!S.getElementsByName(k.expando).length}),m.disconnectedMatch=Ne(function(e){return se.call(e,"*")}),m.scope=Ne(function(){return S.querySelectorAll(":scope")}),m.cssHas=Ne(function(){try{return S.querySelector(":has(*,:jqfake)"),0}catch(e){return 1}}),m.getById?(w.filter.ID=function(e){var t=e.replace(f,d);return function(e){return e.getAttribute("id")===t}},w.find.ID=function(e,t){if(void 0!==t.getElementById&&E)return(t=t.getElementById(e))?[t]:[]}):(w.filter.ID=function(e){var t=e.replace(f,d);return function(e){e=void 0!==e.getAttributeNode&&e.getAttributeNode("id");return e&&e.value===t}},w.find.ID=function(e,t){if(void 0!==t.getElementById&&E){var n,r,o,i=t.getElementById(e);if(i){if((n=i.getAttributeNode("id"))&&n.value===e)return[i];for(o=t.getElementsByName(e),r=0;i=o[r++];)if((n=i.getAttributeNode("id"))&&n.value===e)return[i]}return[]}}),w.find.TAG=function(e,t){return void 0!==t.getElementsByTagName?t.getElementsByTagName(e):t.querySelectorAll(e)},w.find.CLASS=function(e,t){if(void 0!==t.getElementsByClassName&&E)return t.getElementsByClassName(e)},p=[],Ne(function(e){var t;r.appendChild(e).innerHTML="<a id='"+A+"' href='' disabled='disabled'></a><select id='"+A+"-\r\\' disabled='disabled'><option selected=''></option></select>",e.querySelectorAll("[selected]").length||p.push("\\["+n+"*(?:value|"+me+")"),e.querySelectorAll("[id~="+A+"-]").length||p.push("~="),e.querySelectorAll("a#"+A+"+*").length||p.push(".#.+[+~]"),e.querySelectorAll(":checked").length||p.push(":checked"),(t=S.createElement("input")).setAttribute("type","hidden"),e.appendChild(t).setAttribute("name","D"),r.appendChild(e).disabled=!0,2!==e.querySelectorAll(":disabled").length&&p.push(":enabled",":disabled"),(t=S.createElement("input")).setAttribute("name",""),e.appendChild(t),e.querySelectorAll("[name='']").length||p.push("\\["+n+"*name"+n+"*="+n+"*(?:''|\"\")")}),m.cssHas||p.push(":has"),p=p.length&&new RegExp(p.join("|")),he=function(e,t){var n;return e===t?(ae=!0,0):!e.compareDocumentPosition-!t.compareDocumentPosition||(1&(n=(e.ownerDocument||e)==(t.ownerDocument||t)?e.compareDocumentPosition(t):1)||!m.sortDetached&&t.compareDocumentPosition(e)===n?e===S||e.ownerDocument==o&&N.contains(o,e)?-1:t===S||t.ownerDocument==o&&N.contains(o,t)?1:ie?b.call(ie,e)-b.call(ie,t):0:4&n?-1:1)}),S}for(re in N.matches=function(e,t){return N(e,null,null,t)},N.matchesSelector=function(e,t){if(Pe(e),E&&!de[t+" "]&&(!p||!p.test(t)))try{var n=se.call(e,t);if(n||m.disconnectedMatch||e.document&&11!==e.document.nodeType)return n}catch(e){de(t,!0)}return 0<N(t,S,null,[e]).length},N.contains=function(e,t){return(e.ownerDocument||e)!=S&&Pe(e),k.contains(e,t)},N.attr=function(e,t){(e.ownerDocument||e)!=S&&Pe(e);var n=w.attrHandle[t.toLowerCase()],n=n&&z.call(w.attrHandle,t.toLowerCase())?n(e,t,!E):void 0;return void 0!==n?n:e.getAttribute(t)},N.error=function(e){throw new Error("Syntax error, unrecognized expression: "+e)},k.uniqueSort=function(e){var t,n=[],r=0,o=0;if(ae=!m.sortStable,ie=!m.sortStable&&s.call(e,0),Z.call(e,he),ae){for(;t=e[o++];)t===e[o]&&(r=n.push(o));for(;r--;)J.call(e,n[r],1)}return ie=null,e},k.fn.uniqueSort=function(){return this.pushStack(k.uniqueSort(s.apply(this)))},(w=k.expr={cacheLength:50,createPseudo:l,match:Ce,attrHandle:{},find:{},relative:{">":{dir:"parentNode",first:!0}," ":{dir:"parentNode"},"+":{dir:"previousSibling",first:!0},"~":{dir:"previousSibling"}},preFilter:{ATTR:function(e){return e[1]=e[1].replace(f,d),e[3]=(e[3]||e[4]||e[5]||"").replace(f,d),"~="===e[2]&&(e[3]=" "+e[3]+" "),e.slice(0,4)},CHILD:function(e){return e[1]=e[1].toLowerCase(),"nth"===e[1].slice(0,3)?(e[3]||N.error(e[0]),e[4]=+(e[4]?e[5]+(e[6]||1):2*("even"===e[3]||"odd"===e[3])),e[5]=+(e[7]+e[8]||"odd"===e[3])):e[3]&&N.error(e[0]),e},PSEUDO:function(e){var t,n=!e[6]&&e[2];return Ce.CHILD.test(e[0])?null:(e[3]?e[2]=e[4]||e[5]||"":n&&be.test(n)&&(t=(t=qe(n,!0))&&n.indexOf(")",n.length-t)-n.length)&&(e[0]=e[0].slice(0,t),e[2]=n.slice(0,t)),e.slice(0,3))}},filter:{TAG:function(e){var t=e.replace(f,d).toLowerCase();return"*"===e?function(){return!0}:function(e){return x(e,t)}},CLASS:function(e){var t=ue[e+" "];return t||(t=new RegExp("(^|"+n+")"+e+"("+n+"|$)"))&&ue(e,function(e){return t.test("string"==typeof e.className&&e.className||void 0!==e.getAttribute&&e.getAttribute("class")||"")})},ATTR:function(t,n,r){return function(e){e=N.attr(e,t);return null==e?"!="===n:!n||(e+="","="===n?e===r:"!="===n?e!==r:"^="===n?r&&0===e.indexOf(r):"*="===n?r&&-1<e.indexOf(r):"$="===n?r&&e.slice(-r.length)===r:"~="===n?-1<(" "+e.replace(ge," ")+" ").indexOf(r):"|="===n&&(e===r||e.slice(0,r.length+1)===r+"-"))}},CHILD:function(d,e,t,h,m){var g="nth"!==d.slice(0,3),v="last"!==d.slice(-4),y="of-type"===e;return 1===h&&0===m?function(e){return!!e.parentNode}:function(e,t,n){var r,o,i,a,s,l=g!=v?"nextSibling":"previousSibling",c=e.parentNode,u=y&&e.nodeName.toLowerCase(),f=!n&&!y,p=!1;if(c){if(g){for(;l;){for(i=e;i=i[l];)if(y?x(i,u):1===i.nodeType)return!1;s=l="only"===d&&!s&&"nextSibling"}return!0}if(s=[v?c.firstChild:c.lastChild],v&&f){for(p=(a=(r=(o=c[A]||(c[A]={}))[d]||[])[0]===D&&r[1])&&r[2],i=a&&c.childNodes[a];i=++a&&i&&i[l]||(p=a=0,s.pop());)if(1===i.nodeType&&++p&&i===e){o[d]=[D,a,p];break}}else if(!1===(p=f?a=(r=(o=e[A]||(e[A]={}))[d]||[])[0]===D&&r[1]:p))for(;(i=++a&&i&&i[l]||(p=a=0,s.pop()))&&((y?!x(i,u):1!==i.nodeType)||!++p||(f&&((o=i[A]||(i[A]={}))[d]=[D,p]),i!==e)););return(p-=m)===h||p%h==0&&0<=p/h}}},PSEUDO:function(e,i){var t,a=w.pseudos[e]||w.setFilters[e.toLowerCase()]||N.error("unsupported pseudo: "+e);return a[A]?a(i):1<a.length?(t=[e,e,"",i],w.setFilters.hasOwnProperty(e.toLowerCase())?l(function(e,t){for(var n,r=a(e,i),o=r.length;o--;)e[n=b.call(e,r[o])]=!(t[n]=r[o])}):function(e){return a(e,0,t)}):a}},pseudos:{not:l(function(e){var r=[],o=[],s=Fe(e.replace(ee,"$1"));return s[A]?l(function(e,t,n,r){for(var o,i=s(e,null,r,[]),a=e.length;a--;)(o=i[a])&&(e[a]=!(t[a]=o))}):function(e,t,n){return r[0]=e,s(r,null,n,o),r[0]=null,!o.pop()}}),has:l(function(t){return function(e){return 0<N(t,e).length}}),contains:l(function(t){return t=t.replace(f,d),function(e){return-1<(e.textContent||k.text(e)).indexOf(t)}}),lang:l(function(n){return we.test(n||"")||N.error("unsupported lang: "+n),n=n.replace(f,d).toLowerCase(),function(e){var t;do{if(t=E?e.lang:e.getAttribute("xml:lang")||e.getAttribute("lang"))return(t=t.toLowerCase())===n||0===t.indexOf(n+"-")}while((e=e.parentNode)&&1===e.nodeType);return!1}}),target:function(e){var t=C.location&&C.location.hash;return t&&t.slice(1)===e.id},root:function(e){return e===r},focus:function(e){return e===(()=>{try{return S.activeElement}catch(e){}})()&&S.hasFocus()&&!!(e.type||e.href||~e.tabIndex)},enabled:Ie(!1),disabled:Ie(!0),checked:function(e){return x(e,"input")&&!!e.checked||x(e,"option")&&!!e.selected},selected:function(e){return e.parentNode&&e.parentNode.selectedIndex,!0===e.selected},empty:function(e){for(e=e.firstChild;e;e=e.nextSibling)if(e.nodeType<6)return!1;return!0},parent:function(e){return!w.pseudos.empty(e)},header:function(e){return ke.test(e.nodeName)},input:function(e){return Te.test(e.nodeName)},button:function(e){return x(e,"input")&&"button"===e.type||x(e,"button")},text:function(e){return x(e,"input")&&"text"===e.type&&(null==(e=e.getAttribute("type"))||"text"===e.toLowerCase())},first:Oe(function(){return[0]}),last:Oe(function(e,t){return[t-1]}),eq:Oe(function(e,t,n){return[n<0?n+t:n]}),even:Oe(function(e,t){for(var n=0;n<t;n+=2)e.push(n);return e}),odd:Oe(function(e,t){for(var n=1;n<t;n+=2)e.push(n);return e}),lt:Oe(function(e,t,n){for(var r=n<0?n+t:t<n?t:n;0<=--r;)e.push(r);return e}),gt:Oe(function(e,t,n){for(var r=n<0?n+t:n;++r<t;)e.push(r);return e})}}).pseudos.nth=w.pseudos.eq,{radio:!0,checkbox:!0,file:!0,password:!0,image:!0})w.pseudos[re]=(t=>function(e){return x(e,"input")&&e.type===t})(re);for(re in{submit:!0,reset:!0})w.pseudos[re]=(t=>function(e){return(x(e,"input")||x(e,"button"))&&e.type===t})(re);function He(){}function qe(e,t){var n,r,o,i,a,s,l,c=fe[e+" "];if(c)return t?0:c.slice(0);for(a=e,s=[],l=w.preFilter;a;){for(i in n&&!(r=ve.exec(a))||(r&&(a=a.slice(r[0].length)||a),s.push(o=[])),n=!1,(r=ye.exec(a))&&(n=r.shift(),o.push({value:n,type:r[0].replace(ee," ")}),a=a.slice(n.length)),w.filter)!(r=Ce[i].exec(a))||l[i]&&!(r=l[i](r))||(n=r.shift(),o.push({value:n,type:i,matches:r}),a=a.slice(n.length));if(!n)break}return t?a.length:a?N.error(e):fe(e,s).slice(0)}function Me(e){for(var t=0,n=e.length,r="";t<n;t++)r+=e[t].value;return r}function _e(a,e,t){var s=e.dir,l=e.next,c=l||s,u=t&&"parentNode"===c,f=ce++;return e.first?function(e,t,n){for(;e=e[s];)if(1===e.nodeType||u)return a(e,t,n);return!1}:function(e,t,n){var r,o,i=[D,f];if(n){for(;e=e[s];)if((1===e.nodeType||u)&&a(e,t,n))return!0}else for(;e=e[s];)if(1===e.nodeType||u)if(o=e[A]||(e[A]={}),l&&x(e,l))e=e[s]||e;else{if((r=o[c])&&r[0]===D&&r[1]===f)return i[2]=r[2];if((o[c]=i)[2]=a(e,t,n))return!0}return!1}}function $e(o){return 1<o.length?function(e,t,n){for(var r=o.length;r--;)if(!o[r](e,t,n))return!1;return!0}:o[0]}function Be(e,t,n,r,o){for(var i,a=[],s=0,l=e.length,c=null!=t;s<l;s++)!(i=e[s])||n&&!n(i,r,o)||(a.push(i),c&&t.push(s));return a}function Re(d,h,m,g,v,e){return g&&!g[A]&&(g=Re(g)),v&&!v[A]&&(v=Re(v,e)),l(function(e,t,n,r){var o,i,a,s,l=[],c=[],u=t.length,f=e||((e,t,n)=>{for(var r=0,o=t.length;r<o;r++)N(e,t[r],n);return n})(h||"*",n.nodeType?[n]:n,[]),p=!d||!e&&h?f:Be(f,l,d,n,r);if(m?m(p,s=v||(e?d:u||g)?[]:t,n,r):s=p,g)for(o=Be(s,c),g(o,[],n,r),i=o.length;i--;)(a=o[i])&&(s[c[i]]=!(p[c[i]]=a));if(e){if(v||d){if(v){for(o=[],i=s.length;i--;)(a=s[i])&&o.push(p[i]=a);v(null,s=[],o,r)}for(i=s.length;i--;)(a=s[i])&&-1<(o=v?b.call(e,a):l[i])&&(e[o]=!(t[o]=a))}}else s=Be(s===t?s.splice(u,s.length):s),v?v(null,t,s,r):j.apply(t,s)})}function Fe(e,t){var n,g,v,y,x,r,o=[],i=[],a=pe[e+" "];if(!a){for(n=(t=t||qe(e)).length;n--;)((a=function e(t){for(var r,n,o,i=t.length,a=w.relative[t[0].type],s=a||w.relative[" "],l=a?1:0,c=_e(function(e){return e===r},s,!0),u=_e(function(e){return-1<b.call(r,e)},s,!0),f=[function(e,t,n){return e=!a&&(n||t!=oe)||((r=t).nodeType?c:u)(e,t,n),r=null,e}];l<i;l++)if(n=w.relative[t[l].type])f=[_e($e(f),n)];else{if((n=w.filter[t[l].type].apply(null,t[l].matches))[A]){for(o=++l;o<i&&!w.relative[t[o].type];o++);return Re(1<l&&$e(f),1<l&&Me(t.slice(0,l-1).concat({value:" "===t[l-2].type?"*":""})).replace(ee,"$1"),n,l<o&&e(t.slice(l,o)),o<i&&e(t=t.slice(o)),o<i&&Me(t))}f.push(n)}return $e(f)}(t[n]))[A]?o:i).push(a);(a=pe(e,(y=0<(v=o).length,x=0<(g=i).length,r=function(e,t,n,r,o){var i,a,s,l=0,c="0",u=e&&[],f=[],p=oe,d=e||x&&w.find.TAG("*",o),h=D+=null==p?1:Math.random()||.1,m=d.length;for(o&&(oe=t==S||t||o);c!==m&&null!=(i=d[c]);c++){if(x&&i){for(a=0,t||i.ownerDocument==S||(Pe(i),n=!E);s=g[a++];)if(s(i,t||S,n)){j.call(r,i);break}o&&(D=h)}y&&((i=!s&&i)&&l--,e)&&u.push(i)}if(l+=c,y&&c!==l){for(a=0;s=v[a++];)s(u,f,t,n);if(e){if(0<l)for(;c--;)u[c]||f[c]||(f[c]=Q.call(r));f=Be(f)}j.apply(r,f),o&&!e&&0<f.length&&1<l+v.length&&k.uniqueSort(r)}return o&&(D=h,oe=p),u},y?l(r):r))).selector=e}return a}function ze(e,t,n,r){var o,i,a,s,l,c="function"==typeof e&&e,u=!r&&qe(e=c.selector||e);if(n=n||[],1===u.length){if(2<(i=u[0]=u[0].slice(0)).length&&"ID"===(a=i[0]).type&&9===t.nodeType&&E&&w.relative[i[1].type]){if(!(t=(w.find.ID(a.matches[0].replace(f,d),t)||[])[0]))return n;c&&(t=t.parentNode),e=e.slice(i.shift().value.length)}for(o=Ce.needsContext.test(e)?0:i.length;o--&&(a=i[o],!w.relative[s=a.type]);)if((l=w.find[s])&&(r=l(a.matches[0].replace(f,d),Ee.test(i[0].type)&&Le(t.parentNode)||t))){if(i.splice(o,1),e=r.length&&Me(i))break;return j.apply(n,r),n}}return(c||Fe(e,u))(r,t,!E,n,!t||Ee.test(e)&&Le(t.parentNode)||t),n}He.prototype=w.filters=w.pseudos,w.setFilters=new He,m.sortStable=A.split("").sort(he).join("")===A,Pe(),m.sortDetached=Ne(function(e){return 1&e.compareDocumentPosition(S.createElement("fieldset"))}),k.find=N,k.expr[":"]=k.expr.pseudos,k.unique=k.uniqueSort,N.compile=Fe,N.select=ze,N.setDocument=Pe,N.tokenize=qe,N.escape=k.escapeSelector,N.getText=k.text,N.isXML=k.isXMLDoc,N.selectors=k.expr,N.support=k.support,N.uniqueSort=k.uniqueSort;function We(e,t,n){for(var r=[],o=void 0!==n;(e=e[t])&&9!==e.nodeType;)if(1===e.nodeType){if(o&&k(e).is(n))break;r.push(e)}return r}function Ue(e,t){for(var n=[];e;e=e.nextSibling)1===e.nodeType&&e!==t&&n.push(e);return n}var Xe=k.expr.match.needsContext,Ge=/^<([a-z][^\/\0>:\x20\t\r\n\f]*)[\x20\t\r\n\f]*\/?>(?:<\/\1>|)$/i;function Ye(e,n,r){return y(n)?k.grep(e,function(e,t){return!!n.call(e,t,e)!==r}):n.nodeType?k.grep(e,function(e){return e===n!==r}):"string"!=typeof n?k.grep(e,function(e){return-1<b.call(n,e)!==r}):k.filter(n,e,r)}k.filter=function(e,t,n){var r=t[0];return n&&(e=":not("+e+")"),1===t.length&&1===r.nodeType?k.find.matchesSelector(r,e)?[r]:[]:k.find.matches(e,k.grep(t,function(e){return 1===e.nodeType}))},k.fn.extend({find:function(e){var t,n,r=this.length,o=this;if("string"!=typeof e)return this.pushStack(k(e).filter(function(){for(t=0;t<r;t++)if(k.contains(o[t],this))return!0}));for(n=this.pushStack([]),t=0;t<r;t++)k.find(e,o[t],n);return 1<r?k.uniqueSort(n):n},filter:function(e){return this.pushStack(Ye(this,e||[],!1))},not:function(e){return this.pushStack(Ye(this,e||[],!0))},is:function(e){return!!Ye(this,"string"==typeof e&&Xe.test(e)?k(e):e||[],!1).length}});var Ve,Ke=/^(?:\s*(<[\w\W]+>)[^>]*|#([\w-]+))$/,Qe=((k.fn.init=function(e,t,n){if(e){if(n=n||Ve,"string"!=typeof e)return e.nodeType?(this[0]=e,this.length=1,this):y(e)?void 0!==n.ready?n.ready(e):e(k):k.makeArray(e,this);if(!(r="<"===e[0]&&">"===e[e.length-1]&&3<=e.length?[null,e,null]:Ke.exec(e))||!r[1]&&t)return(!t||t.jquery?t||n:this.constructor(t)).find(e);if(r[1]){if(t=t instanceof k?t[0]:t,k.merge(this,k.parseHTML(r[1],t&&t.nodeType?t.ownerDocument||t:T,!0)),Ge.test(r[1])&&k.isPlainObject(t))for(var r in t)y(this[r])?this[r](t[r]):this.attr(r,t[r])}else(n=T.getElementById(r[2]))&&(this[0]=n,this.length=1)}return this}).prototype=k.fn,Ve=k(T),/^(?:parents|prev(?:Until|All))/),Ze={children:!0,contents:!0,next:!0,prev:!0};function Je(e,t){for(;(e=e[t])&&1!==e.nodeType;);return e}k.fn.extend({has:function(e){var t=k(e,this),n=t.length;return this.filter(function(){for(var e=0;e<n;e++)if(k.contains(this,t[e]))return!0})},closest:function(e,t){var n,r=0,o=this.length,i=[],a="string"!=typeof e&&k(e);if(!Xe.test(e))for(;r<o;r++)for(n=this[r];n&&n!==t;n=n.parentNode)if(n.nodeType<11&&(a?-1<a.index(n):1===n.nodeType&&k.find.matchesSelector(n,e))){i.push(n);break}return this.pushStack(1<i.length?k.uniqueSort(i):i)},index:function(e){return e?"string"==typeof e?b.call(k(e),this[0]):b.call(this,e.jquery?e[0]:e):this[0]&&this[0].parentNode?this.first().prevAll().length:-1},add:function(e,t){return this.pushStack(k.uniqueSort(k.merge(this.get(),k(e,t))))},addBack:function(e){return this.add(null==e?this.prevObject:this.prevObject.filter(e))}}),k.each({parent:function(e){e=e.parentNode;return e&&11!==e.nodeType?e:null},parents:function(e){return We(e,"parentNode")},parentsUntil:function(e,t,n){return We(e,"parentNode",n)},next:function(e){return Je(e,"nextSibling")},prev:function(e){return Je(e,"previousSibling")},nextAll:function(e){return We(e,"nextSibling")},prevAll:function(e){return We(e,"previousSibling")},nextUntil:function(e,t,n){return We(e,"nextSibling",n)},prevUntil:function(e,t,n){return We(e,"previousSibling",n)},siblings:function(e){return Ue((e.parentNode||{}).firstChild,e)},children:function(e){return Ue(e.firstChild)},contents:function(e){return null!=e.contentDocument&&_(e.contentDocument)?e.contentDocument:(x(e,"template")&&(e=e.content||e),k.merge([],e.childNodes))}},function(r,o){k.fn[r]=function(e,t){var n=k.map(this,o,e);return(t="Until"!==r.slice(-5)?e:t)&&"string"==typeof t&&(n=k.filter(t,n)),1<this.length&&(Ze[r]||k.uniqueSort(n),Qe.test(r))&&n.reverse(),this.pushStack(n)}});var I=/[^\x20\t\r\n\f]+/g;function et(e){return e}function tt(e){throw e}function nt(e,t,n,r){var o;try{e&&y(o=e.promise)?o.call(e).done(t).fail(n):e&&y(o=e.then)?o.call(e,t,n):t.apply(void 0,[e].slice(r))}catch(e){n.apply(void 0,[e])}}k.Callbacks=function(r){var e,n;r="string"==typeof r?(e=r,n={},k.each(e.match(I)||[],function(e,t){n[t]=!0}),n):k.extend({},r);function o(){for(s=s||r.once,a=i=!0;c.length;u=-1)for(t=c.shift();++u<l.length;)!1===l[u].apply(t[0],t[1])&&r.stopOnFalse&&(u=l.length,t=!1);r.memory||(t=!1),i=!1,s&&(l=t?[]:"")}var i,t,a,s,l=[],c=[],u=-1,f={add:function(){return l&&(t&&!i&&(u=l.length-1,c.push(t)),function n(e){k.each(e,function(e,t){y(t)?r.unique&&f.has(t)||l.push(t):t&&t.length&&"string"!==Y(t)&&n(t)})}(arguments),t)&&!i&&o(),this},remove:function(){return k.each(arguments,function(e,t){for(var n;-1<(n=k.inArray(t,l,n));)l.splice(n,1),n<=u&&u--}),this},has:function(e){return e?-1<k.inArray(e,l):0<l.length},empty:function(){return l=l&&[],this},disable:function(){return s=c=[],l=t="",this},disabled:function(){return!l},lock:function(){return s=c=[],t||i||(l=t=""),this},locked:function(){return!!s},fireWith:function(e,t){return s||(t=[e,(t=t||[]).slice?t.slice():t],c.push(t),i)||o(),this},fire:function(){return f.fireWith(this,arguments),this},fired:function(){return!!a}};return f},k.extend({Deferred:function(e){var i=[["notify","progress",k.Callbacks("memory"),k.Callbacks("memory"),2],["resolve","done",k.Callbacks("once memory"),k.Callbacks("once memory"),0,"resolved"],["reject","fail",k.Callbacks("once memory"),k.Callbacks("once memory"),1,"rejected"]],o="pending",a={state:function(){return o},always:function(){return s.done(arguments).fail(arguments),this},catch:function(e){return a.then(null,e)},pipe:function(){var o=arguments;return k.Deferred(function(r){k.each(i,function(e,t){var n=y(o[t[4]])&&o[t[4]];s[t[1]](function(){var e=n&&n.apply(this,arguments);e&&y(e.promise)?e.promise().progress(r.notify).done(r.resolve).fail(r.reject):r[t[0]+"With"](this,n?[e]:arguments)})}),o=null}).promise()},then:function(t,n,r){var l=0;function c(o,i,a,s){return function(){function e(){var e,t;if(!(o<l)){if((e=a.apply(n,r))===i.promise())throw new TypeError("Thenable self-resolution");t=e&&("object"==typeof e||"function"==typeof e)&&e.then,y(t)?s?t.call(e,c(l,i,et,s),c(l,i,tt,s)):(l++,t.call(e,c(l,i,et,s),c(l,i,tt,s),c(l,i,et,i.notifyWith))):(a!==et&&(n=void 0,r=[e]),(s||i.resolveWith)(n,r))}}var n=this,r=arguments,t=s?e:function(){try{e()}catch(e){k.Deferred.exceptionHook&&k.Deferred.exceptionHook(e,t.error),l<=o+1&&(a!==tt&&(n=void 0,r=[e]),i.rejectWith(n,r))}};o?t():(k.Deferred.getErrorHook?t.error=k.Deferred.getErrorHook():k.Deferred.getStackHook&&(t.error=k.Deferred.getStackHook()),C.setTimeout(t))}}return k.Deferred(function(e){i[0][3].add(c(0,e,y(r)?r:et,e.notifyWith)),i[1][3].add(c(0,e,y(t)?t:et)),i[2][3].add(c(0,e,y(n)?n:tt))}).promise()},promise:function(e){return null!=e?k.extend(e,a):a}},s={};return k.each(i,function(e,t){var n=t[2],r=t[5];a[t[1]]=n.add,r&&n.add(function(){o=r},i[3-e][2].disable,i[3-e][3].disable,i[0][2].lock,i[0][3].lock),n.add(t[3].fire),s[t[0]]=function(){return s[t[0]+"With"](this===s?void 0:this,arguments),this},s[t[0]+"With"]=n.fireWith}),a.promise(s),e&&e.call(s,s),s},when:function(e){function t(t){return function(e){o[t]=this,i[t]=1<arguments.length?s.call(arguments):e,--n||a.resolveWith(o,i)}}var n=arguments.length,r=n,o=Array(r),i=s.call(arguments),a=k.Deferred();if(n<=1&&(nt(e,a.done(t(r)).resolve,a.reject,!n),"pending"===a.state()||y(i[r]&&i[r].then)))return a.then();for(;r--;)nt(i[r],t(r),a.reject);return a.promise()}});var rt=/^(Eval|Internal|Range|Reference|Syntax|Type|URI)Error$/,ot=(k.Deferred.exceptionHook=function(e,t){C.console&&C.console.warn&&e&&rt.test(e.name)&&C.console.warn("jQuery.Deferred exception: "+e.message,e.stack,t)},k.readyException=function(e){C.setTimeout(function(){throw e})},k.Deferred());function it(){T.removeEventListener("DOMContentLoaded",it),C.removeEventListener("load",it),k.ready()}k.fn.ready=function(e){return ot.then(e).catch(function(e){k.readyException(e)}),this},k.extend({isReady:!1,readyWait:1,ready:function(e){(!0===e?--k.readyWait:k.isReady)||(k.isReady=!0)!==e&&0<--k.readyWait||ot.resolveWith(T,[k])}}),k.ready.then=ot.then,"complete"===T.readyState||"loading"!==T.readyState&&!T.documentElement.doScroll?C.setTimeout(k.ready):(T.addEventListener("DOMContentLoaded",it),C.addEventListener("load",it));function u(e,t,n,r,o,i,a){var s=0,l=e.length,c=null==n;if("object"===Y(n))for(s in o=!0,n)u(e,t,s,n[s],!0,i,a);else if(void 0!==r&&(o=!0,y(r)||(a=!0),t=c?a?(t.call(e,r),null):(c=t,function(e,t,n){return c.call(k(e),n)}):t))for(;s<l;s++)t(e[s],n,a?r:r.call(e[s],s,t(e[s],n)));return o?e:c?t.call(e):l?t(e[0],n):i}var at=/^-ms-/,st=/-([a-z])/g;function lt(e,t){return t.toUpperCase()}function O(e){return e.replace(at,"ms-").replace(st,lt)}function ct(e){return 1===e.nodeType||9===e.nodeType||!+e.nodeType}function ut(){this.expando=k.expando+ut.uid++}ut.uid=1,ut.prototype={cache:function(e){var t=e[this.expando];return t||(t={},ct(e)&&(e.nodeType?e[this.expando]=t:Object.defineProperty(e,this.expando,{value:t,configurable:!0}))),t},set:function(e,t,n){var r,o=this.cache(e);if("string"==typeof t)o[O(t)]=n;else for(r in t)o[O(r)]=t[r];return o},get:function(e,t){return void 0===t?this.cache(e):e[this.expando]&&e[this.expando][O(t)]},access:function(e,t,n){return void 0===t||t&&"string"==typeof t&&void 0===n?this.get(e,t):(this.set(e,t,n),void 0!==n?n:t)},remove:function(e,t){var n,r=e[this.expando];if(void 0!==r){if(void 0!==t){n=(t=Array.isArray(t)?t.map(O):(t=O(t))in r?[t]:t.match(I)||[]).length;for(;n--;)delete r[t[n]]}void 0!==t&&!k.isEmptyObject(r)||(e.nodeType?e[this.expando]=void 0:delete e[this.expando])}},hasData:function(e){e=e[this.expando];return void 0!==e&&!k.isEmptyObject(e)}};var v=new ut,c=new ut,ft=/^(?:\{[\w\W]*\}|\[[\w\W]*\])$/,pt=/[A-Z]/g;function dt(e,t,n){var r,o;if(void 0===n&&1===e.nodeType)if(r="data-"+t.replace(pt,"-$&").toLowerCase(),"string"==typeof(n=e.getAttribute(r))){try{n="true"===(o=n)||"false"!==o&&("null"===o?null:o===+o+""?+o:ft.test(o)?JSON.parse(o):o)}catch(e){}c.set(e,t,n)}else n=void 0;return n}k.extend({hasData:function(e){return c.hasData(e)||v.hasData(e)},data:function(e,t,n){return c.access(e,t,n)},removeData:function(e,t){c.remove(e,t)},_data:function(e,t,n){return v.access(e,t,n)},_removeData:function(e,t){v.remove(e,t)}}),k.fn.extend({data:function(n,e){var t,r,o,i=this[0],a=i&&i.attributes;if(void 0!==n)return"object"==typeof n?this.each(function(){c.set(this,n)}):u(this,function(e){var t;if(i&&void 0===e)return void 0!==(t=c.get(i,n))||void 0!==(t=dt(i,n))?t:void 0;this.each(function(){c.set(this,n,e)})},null,e,1<arguments.length,null,!0);if(this.length&&(o=c.get(i),1===i.nodeType)&&!v.get(i,"hasDataAttrs")){for(t=a.length;t--;)a[t]&&0===(r=a[t].name).indexOf("data-")&&(r=O(r.slice(5)),dt(i,r,o[r]));v.set(i,"hasDataAttrs",!0)}return o},removeData:function(e){return this.each(function(){c.remove(this,e)})}}),k.extend({queue:function(e,t,n){var r;if(e)return r=v.get(e,t=(t||"fx")+"queue"),n&&(!r||Array.isArray(n)?r=v.access(e,t,k.makeArray(n)):r.push(n)),r||[]},dequeue:function(e,t){t=t||"fx";var n=k.queue(e,t),r=n.length,o=n.shift(),i=k._queueHooks(e,t);"inprogress"===o&&(o=n.shift(),r--),o&&("fx"===t&&n.unshift("inprogress"),delete i.stop,o.call(e,function(){k.dequeue(e,t)},i)),!r&&i&&i.empty.fire()},_queueHooks:function(e,t){var n=t+"queueHooks";return v.get(e,n)||v.access(e,n,{empty:k.Callbacks("once memory").add(function(){v.remove(e,[t+"queue",n])})})}}),k.fn.extend({queue:function(t,n){var e=2;return"string"!=typeof t&&(n=t,t="fx",e--),arguments.length<e?k.queue(this[0],t):void 0===n?this:this.each(function(){var e=k.queue(this,t,n);k._queueHooks(this,t),"fx"===t&&"inprogress"!==e[0]&&k.dequeue(this,t)})},dequeue:function(e){return this.each(function(){k.dequeue(this,e)})},clearQueue:function(e){return this.queue(e||"fx",[])},promise:function(e,t){function n(){--o||i.resolveWith(a,[a])}var r,o=1,i=k.Deferred(),a=this,s=this.length;for("string"!=typeof e&&(t=e,e=void 0),e=e||"fx";s--;)(r=v.get(a[s],e+"queueHooks"))&&r.empty&&(o++,r.empty.add(n));return n(),i.promise(t)}});function ht(e,t){return"none"===(e=t||e).style.display||""===e.style.display&&yt(e)&&"none"===k.css(e,"display")}var e=/[+-]?(?:\d*\.|)\d+(?:[eE][+-]?\d+|)/.source,mt=new RegExp("^(?:([+-])=|)("+e+")([a-z%]*)$","i"),gt=["Top","Right","Bottom","Left"],vt=T.documentElement,yt=function(e){return k.contains(e.ownerDocument,e)},xt={composed:!0};vt.getRootNode&&(yt=function(e){return k.contains(e.ownerDocument,e)||e.getRootNode(xt)===e.ownerDocument});function bt(e,t,n,r){var o,i,a=20,s=r?function(){return r.cur()}:function(){return k.css(e,t,"")},l=s(),c=n&&n[3]||(k.cssNumber[t]?"":"px"),u=e.nodeType&&(k.cssNumber[t]||"px"!==c&&+l)&&mt.exec(k.css(e,t));if(u&&u[3]!==c){for(c=c||u[3],u=+(l/=2)||1;a--;)k.style(e,t,u+c),(1-i)*(1-(i=s()/l||.5))<=0&&(a=0),u/=i;k.style(e,t,(u*=2)+c),n=n||[]}return n&&(u=+u||+l||0,o=n[1]?u+(n[1]+1)*n[2]:+n[2],r)&&(r.unit=c,r.start=u,r.end=o),o}var wt={};function Ct(e,t){for(var n,r,o,i,a,s,l=[],c=0,u=e.length;c<u;c++)(r=e[c]).style&&(n=r.style.display,t?("none"===n&&(l[c]=v.get(r,"display")||null,l[c]||(r.style.display="")),""===r.style.display&&ht(r)&&(l[c]=(s=i=o=void 0,i=r.ownerDocument,(s=wt[a=r.nodeName])||(o=i.body.appendChild(i.createElement(a)),s=k.css(o,"display"),o.parentNode.removeChild(o),wt[a]=s="none"===s?"block":s)))):"none"!==n&&(l[c]="none",v.set(r,"display",n)));for(c=0;c<u;c++)null!=l[c]&&(e[c].style.display=l[c]);return e}k.fn.extend({show:function(){return Ct(this,!0)},hide:function(){return Ct(this)},toggle:function(e){return"boolean"==typeof e?e?this.show():this.hide():this.each(function(){ht(this)?k(this).show():k(this).hide()})}});var Tt=/^(?:checkbox|radio)$/i,kt=/<([a-z][^\/\0>\x20\t\r\n\f]*)/i,St=/^$|^module$|\/(?:java|ecma)script/i,i=T.createDocumentFragment().appendChild(T.createElement("div")),h=((a=T.createElement("input")).setAttribute("type","radio"),a.setAttribute("checked","checked"),a.setAttribute("name","t"),i.appendChild(a),m.checkClone=i.cloneNode(!0).cloneNode(!0).lastChild.checked,i.innerHTML="<textarea>x</textarea>",m.noCloneChecked=!!i.cloneNode(!0).lastChild.defaultValue,i.innerHTML="<option></option>",m.option=!!i.lastChild,{thead:[1,"<table>","</table>"],col:[2,"<table><colgroup>","</colgroup></table>"],tr:[2,"<table><tbody>","</tbody></table>"],td:[3,"<table><tbody><tr>","</tr></tbody></table>"],_default:[0,"",""]});function g(e,t){var n=void 0!==e.getElementsByTagName?e.getElementsByTagName(t||"*"):void 0!==e.querySelectorAll?e.querySelectorAll(t||"*"):[];return void 0===t||t&&x(e,t)?k.merge([e],n):n}function Et(e,t){for(var n=0,r=e.length;n<r;n++)v.set(e[n],"globalEval",!t||v.get(t[n],"globalEval"))}h.tbody=h.tfoot=h.colgroup=h.caption=h.thead,h.th=h.td,m.option||(h.optgroup=h.option=[1,"<select multiple='multiple'>","</select>"]);var jt=/<|&#?\w+;/;function At(e,t,n,r,o){for(var i,a,s,l,c,u=t.createDocumentFragment(),f=[],p=0,d=e.length;p<d;p++)if((i=e[p])||0===i)if("object"===Y(i))k.merge(f,i.nodeType?[i]:i);else if(jt.test(i)){for(a=a||u.appendChild(t.createElement("div")),s=(kt.exec(i)||["",""])[1].toLowerCase(),s=h[s]||h._default,a.innerHTML=s[1]+k.htmlPrefilter(i)+s[2],c=s[0];c--;)a=a.lastChild;k.merge(f,a.childNodes),(a=u.firstChild).textContent=""}else f.push(t.createTextNode(i));for(u.textContent="",p=0;i=f[p++];)if(r&&-1<k.inArray(i,r))o&&o.push(i);else if(l=yt(i),a=g(u.appendChild(i),"script"),l&&Et(a),n)for(c=0;i=a[c++];)St.test(i.type||"")&&n.push(i);return u}var Dt=/^([^.]*)(?:\.(.+)|)/;function Nt(){return!0}function It(){return!1}function Ot(e,t,n,r,o,i){var a,s;if("object"==typeof t){for(s in"string"!=typeof n&&(r=r||n,n=void 0),t)Ot(e,s,n,r,t[s],i);return e}if(null==r&&null==o?(o=n,r=n=void 0):null==o&&("string"==typeof n?(o=r,r=void 0):(o=r,r=n,n=void 0)),!1===o)o=It;else if(!o)return e;return 1===i&&(a=o,(o=function(e){return k().off(e),a.apply(this,arguments)}).guid=a.guid||(a.guid=k.guid++)),e.each(function(){k.event.add(this,t,o,r,n)})}function Lt(e,r,t){t?(v.set(e,r,!1),k.event.add(e,r,{namespace:!1,handler:function(e){var t,n=v.get(this,r);if(1&e.isTrigger&&this[r]){if(n)(k.event.special[r]||{}).delegateType&&e.stopPropagation();else if(n=s.call(arguments),v.set(this,r,n),this[r](),t=v.get(this,r),v.set(this,r,!1),n!==t)return e.stopImmediatePropagation(),e.preventDefault(),t}else n&&(v.set(this,r,k.event.trigger(n[0],n.slice(1),this)),e.stopPropagation(),e.isImmediatePropagationStopped=Nt)}})):void 0===v.get(e,r)&&k.event.add(e,r,Nt)}k.event={global:{},add:function(t,e,n,r,o){var i,a,s,l,c,u,f,p,d,h=v.get(t);if(ct(t))for(n.handler&&(n=(i=n).handler,o=i.selector),o&&k.find.matchesSelector(vt,o),n.guid||(n.guid=k.guid++),s=(s=h.events)||(h.events=Object.create(null)),a=(a=h.handle)||(h.handle=function(e){return void 0!==k&&k.event.triggered!==e.type?k.event.dispatch.apply(t,arguments):void 0}),l=(e=(e||"").match(I)||[""]).length;l--;)f=d=(p=Dt.exec(e[l])||[])[1],p=(p[2]||"").split(".").sort(),f&&(c=k.event.special[f]||{},f=(o?c.delegateType:c.bindType)||f,c=k.event.special[f]||{},d=k.extend({type:f,origType:d,data:r,handler:n,guid:n.guid,selector:o,needsContext:o&&k.expr.match.needsContext.test(o),namespace:p.join(".")},i),(u=s[f])||((u=s[f]=[]).delegateCount=0,c.setup&&!1!==c.setup.call(t,r,p,a))||t.addEventListener&&t.addEventListener(f,a),c.add&&(c.add.call(t,d),d.handler.guid||(d.handler.guid=n.guid)),o?u.splice(u.delegateCount++,0,d):u.push(d),k.event.global[f]=!0)},remove:function(e,t,n,r,o){var i,a,s,l,c,u,f,p,d,h,m,g=v.hasData(e)&&v.get(e);if(g&&(l=g.events)){for(c=(t=(t||"").match(I)||[""]).length;c--;)if(d=m=(s=Dt.exec(t[c])||[])[1],h=(s[2]||"").split(".").sort(),d){for(f=k.event.special[d]||{},p=l[d=(r?f.delegateType:f.bindType)||d]||[],s=s[2]&&new RegExp("(^|\\.)"+h.join("\\.(?:.*\\.|)")+"(\\.|$)"),a=i=p.length;i--;)u=p[i],!o&&m!==u.origType||n&&n.guid!==u.guid||s&&!s.test(u.namespace)||r&&r!==u.selector&&("**"!==r||!u.selector)||(p.splice(i,1),u.selector&&p.delegateCount--,f.remove&&f.remove.call(e,u));a&&!p.length&&(f.teardown&&!1!==f.teardown.call(e,h,g.handle)||k.removeEvent(e,d,g.handle),delete l[d])}else for(d in l)k.event.remove(e,d+t[c],n,r,!0);k.isEmptyObject(l)&&v.remove(e,"handle events")}},dispatch:function(e){var t,n,r,o,i,a=new Array(arguments.length),s=k.event.fix(e),e=(v.get(this,"events")||Object.create(null))[s.type]||[],l=k.event.special[s.type]||{};for(a[0]=s,t=1;t<arguments.length;t++)a[t]=arguments[t];if(s.delegateTarget=this,!l.preDispatch||!1!==l.preDispatch.call(this,s)){for(i=k.event.handlers.call(this,s,e),t=0;(r=i[t++])&&!s.isPropagationStopped();)for(s.currentTarget=r.elem,n=0;(o=r.handlers[n++])&&!s.isImmediatePropagationStopped();)s.rnamespace&&!1!==o.namespace&&!s.rnamespace.test(o.namespace)||(s.handleObj=o,s.data=o.data,void 0!==(o=((k.event.special[o.origType]||{}).handle||o.handler).apply(r.elem,a))&&!1===(s.result=o)&&(s.preventDefault(),s.stopPropagation()));return l.postDispatch&&l.postDispatch.call(this,s),s.result}},handlers:function(e,t){var n,r,o,i,a,s=[],l=t.delegateCount,c=e.target;if(l&&c.nodeType&&!("click"===e.type&&1<=e.button))for(;c!==this;c=c.parentNode||this)if(1===c.nodeType&&("click"!==e.type||!0!==c.disabled)){for(i=[],a={},n=0;n<l;n++)void 0===a[o=(r=t[n]).selector+" "]&&(a[o]=r.needsContext?-1<k(o,this).index(c):k.find(o,this,null,[c]).length),a[o]&&i.push(r);i.length&&s.push({elem:c,handlers:i})}return c=this,l<t.length&&s.push({elem:c,handlers:t.slice(l)}),s},addProp:function(t,e){Object.defineProperty(k.Event.prototype,t,{enumerable:!0,configurable:!0,get:y(e)?function(){if(this.originalEvent)return e(this.originalEvent)}:function(){if(this.originalEvent)return this.originalEvent[t]},set:function(e){Object.defineProperty(this,t,{enumerable:!0,configurable:!0,writable:!0,value:e})}})},fix:function(e){return e[k.expando]?e:new k.Event(e)},special:{load:{noBubble:!0},click:{setup:function(e){e=this||e;return Tt.test(e.type)&&e.click&&x(e,"input")&&Lt(e,"click",!0),!1},trigger:function(e){e=this||e;return Tt.test(e.type)&&e.click&&x(e,"input")&&Lt(e,"click"),!0},_default:function(e){e=e.target;return Tt.test(e.type)&&e.click&&x(e,"input")&&v.get(e,"click")||x(e,"a")}},beforeunload:{postDispatch:function(e){void 0!==e.result&&e.originalEvent&&(e.originalEvent.returnValue=e.result)}}}},k.removeEvent=function(e,t,n){e.removeEventListener&&e.removeEventListener(t,n)},k.Event=function(e,t){if(!(this instanceof k.Event))return new k.Event(e,t);e&&e.type?(this.originalEvent=e,this.type=e.type,this.isDefaultPrevented=e.defaultPrevented||void 0===e.defaultPrevented&&!1===e.returnValue?Nt:It,this.target=e.target&&3===e.target.nodeType?e.target.parentNode:e.target,this.currentTarget=e.currentTarget,this.relatedTarget=e.relatedTarget):this.type=e,t&&k.extend(this,t),this.timeStamp=e&&e.timeStamp||Date.now(),this[k.expando]=!0},k.Event.prototype={constructor:k.Event,isDefaultPrevented:It,isPropagationStopped:It,isImmediatePropagationStopped:It,isSimulated:!1,preventDefault:function(){var e=this.originalEvent;this.isDefaultPrevented=Nt,e&&!this.isSimulated&&e.preventDefault()},stopPropagation:function(){var e=this.originalEvent;this.isPropagationStopped=Nt,e&&!this.isSimulated&&e.stopPropagation()},stopImmediatePropagation:function(){var e=this.originalEvent;this.isImmediatePropagationStopped=Nt,e&&!this.isSimulated&&e.stopImmediatePropagation(),this.stopPropagation()}},k.each({altKey:!0,bubbles:!0,cancelable:!0,changedTouches:!0,ctrlKey:!0,detail:!0,eventPhase:!0,metaKey:!0,pageX:!0,pageY:!0,shiftKey:!0,view:!0,char:!0,code:!0,charCode:!0,key:!0,keyCode:!0,button:!0,buttons:!0,clientX:!0,clientY:!0,offsetX:!0,offsetY:!0,pointerId:!0,pointerType:!0,screenX:!0,screenY:!0,targetTouches:!0,toElement:!0,touches:!0,which:!0},k.event.addProp),k.each({focus:"focusin",blur:"focusout"},function(r,o){function i(e){var t,n;T.documentMode?(t=v.get(this,"handle"),(n=k.event.fix(e)).type="focusin"===e.type?"focus":"blur",n.isSimulated=!0,t(e),n.target===n.currentTarget&&t(n)):k.event.simulate(o,e.target,k.event.fix(e))}k.event.special[r]={setup:function(){var e;if(Lt(this,r,!0),!T.documentMode)return!1;(e=v.get(this,o))||this.addEventListener(o,i),v.set(this,o,(e||0)+1)},trigger:function(){return Lt(this,r),!0},teardown:function(){var e;if(!T.documentMode)return!1;(e=v.get(this,o)-1)?v.set(this,o,e):(this.removeEventListener(o,i),v.remove(this,o))},_default:function(e){return v.get(e.target,r)},delegateType:o},k.event.special[o]={setup:function(){var e=this.ownerDocument||this.document||this,t=T.documentMode?this:e,n=v.get(t,o);n||(T.documentMode?this.addEventListener(o,i):e.addEventListener(r,i,!0)),v.set(t,o,(n||0)+1)},teardown:function(){var e=this.ownerDocument||this.document||this,t=T.documentMode?this:e,n=v.get(t,o)-1;n?v.set(t,o,n):(T.documentMode?this.removeEventListener(o,i):e.removeEventListener(r,i,!0),v.remove(t,o))}}}),k.each({mouseenter:"mouseover",mouseleave:"mouseout",pointerenter:"pointerover",pointerleave:"pointerout"},function(e,o){k.event.special[e]={delegateType:o,bindType:o,handle:function(e){var t,n=e.relatedTarget,r=e.handleObj;return n&&(n===this||k.contains(this,n))||(e.type=r.origType,t=r.handler.apply(this,arguments),e.type=o),t}}}),k.fn.extend({on:function(e,t,n,r){return Ot(this,e,t,n,r)},one:function(e,t,n,r){return Ot(this,e,t,n,r,1)},off:function(e,t,n){var r,o;if(e&&e.preventDefault&&e.handleObj)r=e.handleObj,k(e.delegateTarget).off(r.namespace?r.origType+"."+r.namespace:r.origType,r.selector,r.handler);else{if("object"!=typeof e)return!1!==t&&"function"!=typeof t||(n=t,t=void 0),!1===n&&(n=It),this.each(function(){k.event.remove(this,e,n,t)});for(o in e)this.off(o,t,e[o])}return this}});var Pt=/<script|<style|<link/i,Ht=/checked\s*(?:[^=]|=\s*.checked.)/i,qt=/^\s*<!\[CDATA\[|\]\]>\s*$/g;function Mt(e,t){return x(e,"table")&&x(11!==t.nodeType?t:t.firstChild,"tr")&&k(e).children("tbody")[0]||e}function _t(e){return e.type=(null!==e.getAttribute("type"))+"/"+e.type,e}function $t(e){return"true/"===(e.type||"").slice(0,5)?e.type=e.type.slice(5):e.removeAttribute("type"),e}function Bt(e,t){var n,r,o,i;if(1===t.nodeType){if(v.hasData(e)&&(i=v.get(e).events))for(o in v.remove(t,"handle events"),i)for(n=0,r=i[o].length;n<r;n++)k.event.add(t,o,i[o][n]);c.hasData(e)&&(e=c.access(e),e=k.extend({},e),c.set(t,e))}}function Rt(n,r,o,i){r=$(r);var e,t,a,s,l,c,u=0,f=n.length,p=f-1,d=r[0],h=y(d);if(h||1<f&&"string"==typeof d&&!m.checkClone&&Ht.test(d))return n.each(function(e){var t=n.eq(e);h&&(r[0]=d.call(this,e,t.html())),Rt(t,r,o,i)});if(f&&(t=(e=At(r,n[0].ownerDocument,!1,n,i)).firstChild,1===e.childNodes.length&&(e=t),t||i)){for(s=(a=k.map(g(e,"script"),_t)).length;u<f;u++)l=e,u!==p&&(l=k.clone(l,!0,!0),s)&&k.merge(a,g(l,"script")),o.call(n[u],l,u);if(s)for(c=a[a.length-1].ownerDocument,k.map(a,$t),u=0;u<s;u++)l=a[u],St.test(l.type||"")&&!v.access(l,"globalEval")&&k.contains(c,l)&&(l.src&&"module"!==(l.type||"").toLowerCase()?k._evalUrl&&!l.noModule&&k._evalUrl(l.src,{nonce:l.nonce||l.getAttribute("nonce")},c):G(l.textContent.replace(qt,""),l,c))}return n}function Ft(e,t,n){for(var r,o=t?k.filter(t,e):e,i=0;null!=(r=o[i]);i++)n||1!==r.nodeType||k.cleanData(g(r)),r.parentNode&&(n&&yt(r)&&Et(g(r,"script")),r.parentNode.removeChild(r));return e}k.extend({htmlPrefilter:function(e){return e},clone:function(e,t,n){var r,o,i,a,s,l,c,u=e.cloneNode(!0),f=yt(e);if(!(m.noCloneChecked||1!==e.nodeType&&11!==e.nodeType||k.isXMLDoc(e)))for(a=g(u),r=0,o=(i=g(e)).length;r<o;r++)s=i[r],"input"===(c=(l=a[r]).nodeName.toLowerCase())&&Tt.test(s.type)?l.checked=s.checked:"input"!==c&&"textarea"!==c||(l.defaultValue=s.defaultValue);if(t)if(n)for(i=i||g(e),a=a||g(u),r=0,o=i.length;r<o;r++)Bt(i[r],a[r]);else Bt(e,u);return 0<(a=g(u,"script")).length&&Et(a,!f&&g(e,"script")),u},cleanData:function(e){for(var t,n,r,o=k.event.special,i=0;void 0!==(n=e[i]);i++)if(ct(n)){if(t=n[v.expando]){if(t.events)for(r in t.events)o[r]?k.event.remove(n,r):k.removeEvent(n,r,t.handle);n[v.expando]=void 0}n[c.expando]&&(n[c.expando]=void 0)}}}),k.fn.extend({detach:function(e){return Ft(this,e,!0)},remove:function(e){return Ft(this,e)},text:function(e){return u(this,function(e){return void 0===e?k.text(this):this.empty().each(function(){1!==this.nodeType&&11!==this.nodeType&&9!==this.nodeType||(this.textContent=e)})},null,e,arguments.length)},append:function(){return Rt(this,arguments,function(e){1!==this.nodeType&&11!==this.nodeType&&9!==this.nodeType||Mt(this,e).appendChild(e)})},prepend:function(){return Rt(this,arguments,function(e){var t;1!==this.nodeType&&11!==this.nodeType&&9!==this.nodeType||(t=Mt(this,e)).insertBefore(e,t.firstChild)})},before:function(){return Rt(this,arguments,function(e){this.parentNode&&this.parentNode.insertBefore(e,this)})},after:function(){return Rt(this,arguments,function(e){this.parentNode&&this.parentNode.insertBefore(e,this.nextSibling)})},empty:function(){for(var e,t=0;null!=(e=this[t]);t++)1===e.nodeType&&(k.cleanData(g(e,!1)),e.textContent="");return this},clone:function(e,t){return e=null!=e&&e,t=null==t?e:t,this.map(function(){return k.clone(this,e,t)})},html:function(e){return u(this,function(e){var t=this[0]||{},n=0,r=this.length;if(void 0===e&&1===t.nodeType)return t.innerHTML;if("string"==typeof e&&!Pt.test(e)&&!h[(kt.exec(e)||["",""])[1].toLowerCase()]){e=k.htmlPrefilter(e);try{for(;n<r;n++)1===(t=this[n]||{}).nodeType&&(k.cleanData(g(t,!1)),t.innerHTML=e);t=0}catch(e){}}t&&this.empty().append(e)},null,e,arguments.length)},replaceWith:function(){var n=[];return Rt(this,arguments,function(e){var t=this.parentNode;k.inArray(this,n)<0&&(k.cleanData(g(this)),t)&&t.replaceChild(e,this)},n)}}),k.each({appendTo:"append",prependTo:"prepend",insertBefore:"before",insertAfter:"after",replaceAll:"replaceWith"},function(e,a){k.fn[e]=function(e){for(var t,n=[],r=k(e),o=r.length-1,i=0;i<=o;i++)t=i===o?this:this.clone(!0),k(r[i])[a](t),B.apply(n,t.get());return this.pushStack(n)}});function zt(e){var t=e.ownerDocument.defaultView;return(t=t&&t.opener?t:C).getComputedStyle(e)}function Wt(e,t,n){var r,o={};for(r in t)o[r]=e.style[r],e.style[r]=t[r];for(r in n=n.call(e),t)e.style[r]=o[r];return n}var Ut,Xt,Gt,Yt,Vt,Kt,Qt,L,Zt=new RegExp("^("+e+")(?!px)[a-z%]+$","i"),Jt=/^--/,en=new RegExp(gt.join("|"),"i");function tn(e,t,n){var r,o=Jt.test(t),i=e.style;return(n=n||zt(e))&&(r=n.getPropertyValue(t)||n[t],""!==(r=o?r&&(r.replace(ee,"$1")||void 0):r)||yt(e)||(r=k.style(e,t)),!m.pixelBoxStyles())&&Zt.test(r)&&en.test(t)&&(o=i.width,e=i.minWidth,t=i.maxWidth,i.minWidth=i.maxWidth=i.width=r,r=n.width,i.width=o,i.minWidth=e,i.maxWidth=t),void 0!==r?r+"":r}function nn(e,t){return{get:function(){if(!e())return(this.get=t).apply(this,arguments);delete this.get}}}function rn(){var e;L&&(Qt.style.cssText="position:absolute;left:-11111px;width:60px;margin-top:1px;padding:0;border:0",L.style.cssText="position:relative;display:block;box-sizing:border-box;overflow:scroll;margin:auto;border:1px;padding:1px;width:60%;top:1%",vt.appendChild(Qt).appendChild(L),e=C.getComputedStyle(L),Ut="1%"!==e.top,Kt=12===on(e.marginLeft),L.style.right="60%",Yt=36===on(e.right),Xt=36===on(e.width),L.style.position="absolute",Gt=12===on(L.offsetWidth/3),vt.removeChild(Qt),L=null)}function on(e){return Math.round(parseFloat(e))}Qt=T.createElement("div"),(L=T.createElement("div")).style&&(L.style.backgroundClip="content-box",L.cloneNode(!0).style.backgroundClip="",m.clearCloneStyle="content-box"===L.style.backgroundClip,k.extend(m,{boxSizingReliable:function(){return rn(),Xt},pixelBoxStyles:function(){return rn(),Yt},pixelPosition:function(){return rn(),Ut},reliableMarginLeft:function(){return rn(),Kt},scrollboxSize:function(){return rn(),Gt},reliableTrDimensions:function(){var e,t,n;return null==Vt&&(e=T.createElement("table"),t=T.createElement("tr"),n=T.createElement("div"),e.style.cssText="position:absolute;left:-11111px;border-collapse:separate",t.style.cssText="box-sizing:content-box;border:1px solid",t.style.height="1px",n.style.height="9px",n.style.display="block",vt.appendChild(e).appendChild(t).appendChild(n),n=C.getComputedStyle(t),Vt=parseInt(n.height,10)+parseInt(n.borderTopWidth,10)+parseInt(n.borderBottomWidth,10)===t.offsetHeight,vt.removeChild(e)),Vt}}));var an=["Webkit","Moz","ms"],sn=T.createElement("div").style,ln={};function cn(e){return k.cssProps[e]||ln[e]||(e in sn?e:ln[e]=(e=>{for(var t=e[0].toUpperCase()+e.slice(1),n=an.length;n--;)if((e=an[n]+t)in sn)return e})(e)||e)}var un=/^(none|table(?!-c[ea]).+)/,fn={position:"absolute",visibility:"hidden",display:"block"},pn={letterSpacing:"0",fontWeight:"400"};function dn(e,t,n){var r=mt.exec(t);return r?Math.max(0,r[2]-(n||0))+(r[3]||"px"):t}function hn(e,t,n,r,o,i){var a="width"===t?1:0,s=0,l=0,c=0;if(n===(r?"border":"content"))return 0;for(;a<4;a+=2)"margin"===n&&(c+=k.css(e,n+gt[a],!0,o)),r?("content"===n&&(l-=k.css(e,"padding"+gt[a],!0,o)),"margin"!==n&&(l-=k.css(e,"border"+gt[a]+"Width",!0,o))):(l+=k.css(e,"padding"+gt[a],!0,o),"padding"!==n?l+=k.css(e,"border"+gt[a]+"Width",!0,o):s+=k.css(e,"border"+gt[a]+"Width",!0,o));return!r&&0<=i&&(l+=Math.max(0,Math.ceil(e["offset"+t[0].toUpperCase()+t.slice(1)]-i-l-s-.5))||0),l+c}function mn(e,t,n){var r=zt(e),o=(!m.boxSizingReliable()||n)&&"border-box"===k.css(e,"boxSizing",!1,r),i=o,a=tn(e,t,r),s="offset"+t[0].toUpperCase()+t.slice(1);if(Zt.test(a)){if(!n)return a;a="auto"}return(!m.boxSizingReliable()&&o||!m.reliableTrDimensions()&&x(e,"tr")||"auto"===a||!parseFloat(a)&&"inline"===k.css(e,"display",!1,r))&&e.getClientRects().length&&(o="border-box"===k.css(e,"boxSizing",!1,r),i=s in e)&&(a=e[s]),(a=parseFloat(a)||0)+hn(e,t,n||(o?"border":"content"),i,r,a)+"px"}function P(e,t,n,r,o){return new P.prototype.init(e,t,n,r,o)}k.extend({cssHooks:{opacity:{get:function(e,t){if(t)return""===(t=tn(e,"opacity"))?"1":t}}},cssNumber:{animationIterationCount:!0,aspectRatio:!0,borderImageSlice:!0,columnCount:!0,flexGrow:!0,flexShrink:!0,fontWeight:!0,gridArea:!0,gridColumn:!0,gridColumnEnd:!0,gridColumnStart:!0,gridRow:!0,gridRowEnd:!0,gridRowStart:!0,lineHeight:!0,opacity:!0,order:!0,orphans:!0,scale:!0,widows:!0,zIndex:!0,zoom:!0,fillOpacity:!0,floodOpacity:!0,stopOpacity:!0,strokeMiterlimit:!0,strokeOpacity:!0},cssProps:{},style:function(e,t,n,r){if(e&&3!==e.nodeType&&8!==e.nodeType&&e.style){var o,i,a,s=O(t),l=Jt.test(t),c=e.style;if(l||(t=cn(s)),a=k.cssHooks[t]||k.cssHooks[s],void 0===n)return a&&"get"in a&&void 0!==(o=a.get(e,!1,r))?o:c[t];"string"==(i=typeof n)&&(o=mt.exec(n))&&o[1]&&(n=bt(e,t,o),i="number"),null!=n&&n==n&&("number"!==i||l||(n+=o&&o[3]||(k.cssNumber[s]?"":"px")),m.clearCloneStyle||""!==n||0!==t.indexOf("background")||(c[t]="inherit"),a&&"set"in a&&void 0===(n=a.set(e,n,r))||(l?c.setProperty(t,n):c[t]=n))}},css:function(e,t,n,r){var o,i=O(t);return Jt.test(t)||(t=cn(i)),"normal"===(o=void 0===(o=(i=k.cssHooks[t]||k.cssHooks[i])&&"get"in i?i.get(e,!0,n):o)?tn(e,t,r):o)&&t in pn&&(o=pn[t]),(""===n||n)&&(i=parseFloat(o),!0===n||isFinite(i))?i||0:o}}),k.each(["height","width"],function(e,a){k.cssHooks[a]={get:function(e,t,n){if(t)return!un.test(k.css(e,"display"))||e.getClientRects().length&&e.getBoundingClientRect().width?mn(e,a,n):Wt(e,fn,function(){return mn(e,a,n)})},set:function(e,t,n){var r=zt(e),o=!m.scrollboxSize()&&"absolute"===r.position,i=(o||n)&&"border-box"===k.css(e,"boxSizing",!1,r),n=n?hn(e,a,n,i,r):0;return i&&o&&(n-=Math.ceil(e["offset"+a[0].toUpperCase()+a.slice(1)]-parseFloat(r[a])-hn(e,a,"border",!1,r)-.5)),n&&(i=mt.exec(t))&&"px"!==(i[3]||"px")&&(e.style[a]=t,t=k.css(e,a)),dn(0,t,n)}}}),k.cssHooks.marginLeft=nn(m.reliableMarginLeft,function(e,t){if(t)return(parseFloat(tn(e,"marginLeft"))||e.getBoundingClientRect().left-Wt(e,{marginLeft:0},function(){return e.getBoundingClientRect().left}))+"px"}),k.each({margin:"",padding:"",border:"Width"},function(o,i){k.cssHooks[o+i]={expand:function(e){for(var t=0,n={},r="string"==typeof e?e.split(" "):[e];t<4;t++)n[o+gt[t]+i]=r[t]||r[t-2]||r[0];return n}},"margin"!==o&&(k.cssHooks[o+i].set=dn)}),k.fn.extend({css:function(e,t){return u(this,function(e,t,n){var r,o,i={},a=0;if(Array.isArray(t)){for(r=zt(e),o=t.length;a<o;a++)i[t[a]]=k.css(e,t[a],!1,r);return i}return void 0!==n?k.style(e,t,n):k.css(e,t)},e,t,1<arguments.length)}}),((k.Tween=P).prototype={constructor:P,init:function(e,t,n,r,o,i){this.elem=e,this.prop=n,this.easing=o||k.easing._default,this.options=t,this.start=this.now=this.cur(),this.end=r,this.unit=i||(k.cssNumber[n]?"":"px")},cur:function(){var e=P.propHooks[this.prop];return(e&&e.get?e:P.propHooks._default).get(this)},run:function(e){var t,n=P.propHooks[this.prop];return this.options.duration?this.pos=t=k.easing[this.easing](e,this.options.duration*e,0,1,this.options.duration):this.pos=t=e,this.now=(this.end-this.start)*t+this.start,this.options.step&&this.options.step.call(this.elem,this.now,this),(n&&n.set?n:P.propHooks._default).set(this),this}}).init.prototype=P.prototype,(P.propHooks={_default:{get:function(e){return 1!==e.elem.nodeType||null!=e.elem[e.prop]&&null==e.elem.style[e.prop]?e.elem[e.prop]:(e=k.css(e.elem,e.prop,""))&&"auto"!==e?e:0},set:function(e){k.fx.step[e.prop]?k.fx.step[e.prop](e):1!==e.elem.nodeType||!k.cssHooks[e.prop]&&null==e.elem.style[cn(e.prop)]?e.elem[e.prop]=e.now:k.style(e.elem,e.prop,e.now+e.unit)}}}).scrollTop=P.propHooks.scrollLeft={set:function(e){e.elem.nodeType&&e.elem.parentNode&&(e.elem[e.prop]=e.now)}},k.easing={linear:function(e){return e},swing:function(e){return.5-Math.cos(e*Math.PI)/2},_default:"swing"},k.fx=P.prototype.init,k.fx.step={};var gn,vn,yn=/^(?:toggle|show|hide)$/,xn=/queueHooks$/;function bn(){vn&&(!1===T.hidden&&C.requestAnimationFrame?C.requestAnimationFrame(bn):C.setTimeout(bn,k.fx.interval),k.fx.tick())}function wn(){return C.setTimeout(function(){gn=void 0}),gn=Date.now()}function Cn(e,t){var n,r=0,o={height:e};for(t=t?1:0;r<4;r+=2-t)o["margin"+(n=gt[r])]=o["padding"+n]=e;return t&&(o.opacity=o.width=e),o}function Tn(e,t,n){for(var r,o=(H.tweeners[t]||[]).concat(H.tweeners["*"]),i=0,a=o.length;i<a;i++)if(r=o[i].call(n,t,e))return r}function H(o,e,t){var n,i,r,a,s,l,c,u=0,f=H.prefilters.length,p=k.Deferred().always(function(){delete d.elem}),d=function(){if(i)return!1;for(var e=gn||wn(),e=Math.max(0,h.startTime+h.duration-e),t=1-(e/h.duration||0),n=0,r=h.tweens.length;n<r;n++)h.tweens[n].run(t);return p.notifyWith(o,[h,t,e]),t<1&&r?e:(r||p.notifyWith(o,[h,1,0]),p.resolveWith(o,[h]),!1)},h=p.promise({elem:o,props:k.extend({},e),opts:k.extend(!0,{specialEasing:{},easing:k.easing._default},t),originalProperties:e,originalOptions:t,startTime:gn||wn(),duration:t.duration,tweens:[],createTween:function(e,t){t=k.Tween(o,h.opts,e,t,h.opts.specialEasing[e]||h.opts.easing);return h.tweens.push(t),t},stop:function(e){var t=0,n=e?h.tweens.length:0;if(!i){for(i=!0;t<n;t++)h.tweens[t].run(1);e?(p.notifyWith(o,[h,1,0]),p.resolveWith(o,[h,e])):p.rejectWith(o,[h,e])}return this}}),m=h.props,g=m,v=h.opts.specialEasing;for(r in g)if(s=v[a=O(r)],l=g[r],Array.isArray(l)&&(s=l[1],l=g[r]=l[0]),r!==a&&(g[a]=l,delete g[r]),(c=k.cssHooks[a])&&"expand"in c)for(r in l=c.expand(l),delete g[a],l)r in g||(g[r]=l[r],v[r]=s);else v[a]=s;for(;u<f;u++)if(n=H.prefilters[u].call(h,o,m,h.opts))return y(n.stop)&&(k._queueHooks(h.elem,h.opts.queue).stop=n.stop.bind(n)),n;return k.map(m,Tn,h),y(h.opts.start)&&h.opts.start.call(o,h),h.progress(h.opts.progress).done(h.opts.done,h.opts.complete).fail(h.opts.fail).always(h.opts.always),k.fx.timer(k.extend(d,{elem:o,anim:h,queue:h.opts.queue})),h}k.Animation=k.extend(H,{tweeners:{"*":[function(e,t){var n=this.createTween(e,t);return bt(n.elem,e,mt.exec(t),n),n}]},tweener:function(e,t){for(var n,r=0,o=(e=y(e)?(t=e,["*"]):e.match(I)).length;r<o;r++)n=e[r],H.tweeners[n]=H.tweeners[n]||[],H.tweeners[n].unshift(t)},prefilters:[function(e,t,n){var r,o,i,a,s,l,c,u="width"in t||"height"in t,f=this,p={},d=e.style,h=e.nodeType&&ht(e),m=v.get(e,"fxshow");for(r in n.queue||(null==(a=k._queueHooks(e,"fx")).unqueued&&(a.unqueued=0,s=a.empty.fire,a.empty.fire=function(){a.unqueued||s()}),a.unqueued++,f.always(function(){f.always(function(){a.unqueued--,k.queue(e,"fx").length||a.empty.fire()})})),t)if(o=t[r],yn.test(o)){if(delete t[r],i=i||"toggle"===o,o===(h?"hide":"show")){if("show"!==o||!m||void 0===m[r])continue;h=!0}p[r]=m&&m[r]||k.style(e,r)}if((l=!k.isEmptyObject(t))||!k.isEmptyObject(p))for(r in u&&1===e.nodeType&&(n.overflow=[d.overflow,d.overflowX,d.overflowY],null==(c=m&&m.display)&&(c=v.get(e,"display")),"none"===(u=k.css(e,"display"))&&(c?u=c:(Ct([e],!0),c=e.style.display||c,u=k.css(e,"display"),Ct([e]))),"inline"===u||"inline-block"===u&&null!=c)&&"none"===k.css(e,"float")&&(l||(f.done(function(){d.display=c}),null==c&&(u=d.display,c="none"===u?"":u)),d.display="inline-block"),n.overflow&&(d.overflow="hidden",f.always(function(){d.overflow=n.overflow[0],d.overflowX=n.overflow[1],d.overflowY=n.overflow[2]})),l=!1,p)l||(m?"hidden"in m&&(h=m.hidden):m=v.access(e,"fxshow",{display:c}),i&&(m.hidden=!h),h&&Ct([e],!0),f.done(function(){for(r in h||Ct([e]),v.remove(e,"fxshow"),p)k.style(e,r,p[r])})),l=Tn(h?m[r]:0,r,f),r in m||(m[r]=l.start,h&&(l.end=l.start,l.start=0))}],prefilter:function(e,t){t?H.prefilters.unshift(e):H.prefilters.push(e)}}),k.speed=function(e,t,n){var r=e&&"object"==typeof e?k.extend({},e):{complete:n||!n&&t||y(e)&&e,duration:e,easing:n&&t||t&&!y(t)&&t};return k.fx.off?r.duration=0:"number"!=typeof r.duration&&(r.duration in k.fx.speeds?r.duration=k.fx.speeds[r.duration]:r.duration=k.fx.speeds._default),null!=r.queue&&!0!==r.queue||(r.queue="fx"),r.old=r.complete,r.complete=function(){y(r.old)&&r.old.call(this),r.queue&&k.dequeue(this,r.queue)},r},k.fn.extend({fadeTo:function(e,t,n,r){return this.filter(ht).css("opacity",0).show().end().animate({opacity:t},e,n,r)},animate:function(t,e,n,r){function o(){var e=H(this,k.extend({},t),a);(i||v.get(this,"finish"))&&e.stop(!0)}var i=k.isEmptyObject(t),a=k.speed(e,n,r);return o.finish=o,i||!1===a.queue?this.each(o):this.queue(a.queue,o)},stop:function(o,e,i){function a(e){var t=e.stop;delete e.stop,t(i)}return"string"!=typeof o&&(i=e,e=o,o=void 0),e&&this.queue(o||"fx",[]),this.each(function(){var e=!0,t=null!=o&&o+"queueHooks",n=k.timers,r=v.get(this);if(t)r[t]&&r[t].stop&&a(r[t]);else for(t in r)r[t]&&r[t].stop&&xn.test(t)&&a(r[t]);for(t=n.length;t--;)n[t].elem!==this||null!=o&&n[t].queue!==o||(n[t].anim.stop(i),e=!1,n.splice(t,1));!e&&i||k.dequeue(this,o)})},finish:function(a){return!1!==a&&(a=a||"fx"),this.each(function(){var e,t=v.get(this),n=t[a+"queue"],r=t[a+"queueHooks"],o=k.timers,i=n?n.length:0;for(t.finish=!0,k.queue(this,a,[]),r&&r.stop&&r.stop.call(this,!0),e=o.length;e--;)o[e].elem===this&&o[e].queue===a&&(o[e].anim.stop(!0),o.splice(e,1));for(e=0;e<i;e++)n[e]&&n[e].finish&&n[e].finish.call(this);delete t.finish})}}),k.each(["toggle","show","hide"],function(e,r){var o=k.fn[r];k.fn[r]=function(e,t,n){return null==e||"boolean"==typeof e?o.apply(this,arguments):this.animate(Cn(r,!0),e,t,n)}}),k.each({slideDown:Cn("show"),slideUp:Cn("hide"),slideToggle:Cn("toggle"),fadeIn:{opacity:"show"},fadeOut:{opacity:"hide"},fadeToggle:{opacity:"toggle"}},function(e,r){k.fn[e]=function(e,t,n){return this.animate(r,e,t,n)}}),k.timers=[],k.fx.tick=function(){var e,t=0,n=k.timers;for(gn=Date.now();t<n.length;t++)(e=n[t])()||n[t]!==e||n.splice(t--,1);n.length||k.fx.stop(),gn=void 0},k.fx.timer=function(e){k.timers.push(e),k.fx.start()},k.fx.interval=13,k.fx.start=function(){vn||(vn=!0,bn())},k.fx.stop=function(){vn=null},k.fx.speeds={slow:600,fast:200,_default:400},k.fn.delay=function(r,e){return r=k.fx&&k.fx.speeds[r]||r,this.queue(e=e||"fx",function(e,t){var n=C.setTimeout(e,r);t.stop=function(){C.clearTimeout(n)}})},a=T.createElement("input"),i=T.createElement("select").appendChild(T.createElement("option")),a.type="checkbox",m.checkOn=""!==a.value,m.optSelected=i.selected,(a=T.createElement("input")).value="t",a.type="radio",m.radioValue="t"===a.value;var kn,Sn=k.expr.attrHandle,En=(k.fn.extend({attr:function(e,t){return u(this,k.attr,e,t,1<arguments.length)},removeAttr:function(e){return this.each(function(){k.removeAttr(this,e)})}}),k.extend({attr:function(e,t,n){var r,o,i=e.nodeType;if(3!==i&&8!==i&&2!==i)return void 0===e.getAttribute?k.prop(e,t,n):(1===i&&k.isXMLDoc(e)||(o=k.attrHooks[t.toLowerCase()]||(k.expr.match.bool.test(t)?kn:void 0)),void 0!==n?null===n?void k.removeAttr(e,t):o&&"set"in o&&void 0!==(r=o.set(e,n,t))?r:(e.setAttribute(t,n+""),n):!(o&&"get"in o&&null!==(r=o.get(e,t)))&&null==(r=k.find.attr(e,t))?void 0:r)},attrHooks:{type:{set:function(e,t){var n;if(!m.radioValue&&"radio"===t&&x(e,"input"))return n=e.value,e.setAttribute("type",t),n&&(e.value=n),t}}},removeAttr:function(e,t){var n,r=0,o=t&&t.match(I);if(o&&1===e.nodeType)for(;n=o[r++];)e.removeAttribute(n)}}),kn={set:function(e,t,n){return!1===t?k.removeAttr(e,n):e.setAttribute(n,n),n}},k.each(k.expr.match.bool.source.match(/\w+/g),function(e,t){var a=Sn[t]||k.find.attr;Sn[t]=function(e,t,n){var r,o,i=t.toLowerCase();return n||(o=Sn[i],Sn[i]=r,r=null!=a(e,t,n)?i:null,Sn[i]=o),r}}),/^(?:input|select|textarea|button)$/i),jn=/^(?:a|area)$/i;function An(e){return(e.match(I)||[]).join(" ")}function Dn(e){return e.getAttribute&&e.getAttribute("class")||""}function Nn(e){return Array.isArray(e)?e:"string"==typeof e&&e.match(I)||[]}k.fn.extend({prop:function(e,t){return u(this,k.prop,e,t,1<arguments.length)},removeProp:function(e){return this.each(function(){delete this[k.propFix[e]||e]})}}),k.extend({prop:function(e,t,n){var r,o,i=e.nodeType;if(3!==i&&8!==i&&2!==i)return 1===i&&k.isXMLDoc(e)||(t=k.propFix[t]||t,o=k.propHooks[t]),void 0!==n?o&&"set"in o&&void 0!==(r=o.set(e,n,t))?r:e[t]=n:o&&"get"in o&&null!==(r=o.get(e,t))?r:e[t]},propHooks:{tabIndex:{get:function(e){var t=k.find.attr(e,"tabindex");return t?parseInt(t,10):En.test(e.nodeName)||jn.test(e.nodeName)&&e.href?0:-1}}},propFix:{for:"htmlFor",class:"className"}}),m.optSelected||(k.propHooks.selected={get:function(e){e=e.parentNode;return e&&e.parentNode&&e.parentNode.selectedIndex,null},set:function(e){e=e.parentNode;e&&(e.selectedIndex,e.parentNode)&&e.parentNode.selectedIndex}}),k.each(["tabIndex","readOnly","maxLength","cellSpacing","cellPadding","rowSpan","colSpan","useMap","frameBorder","contentEditable"],function(){k.propFix[this.toLowerCase()]=this}),k.fn.extend({addClass:function(t){var e,n,r,o,i,a;return y(t)?this.each(function(e){k(this).addClass(t.call(this,e,Dn(this)))}):(e=Nn(t)).length?this.each(function(){if(r=Dn(this),n=1===this.nodeType&&" "+An(r)+" "){for(i=0;i<e.length;i++)o=e[i],n.indexOf(" "+o+" ")<0&&(n+=o+" ");a=An(n),r!==a&&this.setAttribute("class",a)}}):this},removeClass:function(t){var e,n,r,o,i,a;return y(t)?this.each(function(e){k(this).removeClass(t.call(this,e,Dn(this)))}):arguments.length?(e=Nn(t)).length?this.each(function(){if(r=Dn(this),n=1===this.nodeType&&" "+An(r)+" "){for(i=0;i<e.length;i++)for(o=e[i];-1<n.indexOf(" "+o+" ");)n=n.replace(" "+o+" "," ");a=An(n),r!==a&&this.setAttribute("class",a)}}):this:this.attr("class","")},toggleClass:function(t,n){var e,r,o,i,a=typeof t,s="string"==a||Array.isArray(t);return y(t)?this.each(function(e){k(this).toggleClass(t.call(this,e,Dn(this),n),n)}):"boolean"==typeof n&&s?n?this.addClass(t):this.removeClass(t):(e=Nn(t),this.each(function(){if(s)for(i=k(this),o=0;o<e.length;o++)r=e[o],i.hasClass(r)?i.removeClass(r):i.addClass(r);else void 0!==t&&"boolean"!=a||((r=Dn(this))&&v.set(this,"__className__",r),this.setAttribute&&this.setAttribute("class",!r&&!1!==t&&v.get(this,"__className__")||""))}))},hasClass:function(e){for(var t,n=0,r=" "+e+" ";t=this[n++];)if(1===t.nodeType&&-1<(" "+An(Dn(t))+" ").indexOf(r))return!0;return!1}});function In(e){e.stopPropagation()}var On=/\r/g,Ln=(k.fn.extend({val:function(t){var n,e,r,o=this[0];return arguments.length?(r=y(t),this.each(function(e){1===this.nodeType&&(null==(e=r?t.call(this,e,k(this).val()):t)?e="":"number"==typeof e?e+="":Array.isArray(e)&&(e=k.map(e,function(e){return null==e?"":e+""})),(n=k.valHooks[this.type]||k.valHooks[this.nodeName.toLowerCase()])&&"set"in n&&void 0!==n.set(this,e,"value")||(this.value=e))})):o?(n=k.valHooks[o.type]||k.valHooks[o.nodeName.toLowerCase()])&&"get"in n&&void 0!==(e=n.get(o,"value"))?e:"string"==typeof(e=o.value)?e.replace(On,""):null==e?"":e:void 0}}),k.extend({valHooks:{option:{get:function(e){var t=k.find.attr(e,"value");return null!=t?t:An(k.text(e))}},select:{get:function(e){for(var t,n=e.options,r=e.selectedIndex,o="select-one"===e.type,i=o?null:[],a=o?r+1:n.length,s=r<0?a:o?r:0;s<a;s++)if(((t=n[s]).selected||s===r)&&!t.disabled&&(!t.parentNode.disabled||!x(t.parentNode,"optgroup"))){if(t=k(t).val(),o)return t;i.push(t)}return i},set:function(e,t){for(var n,r,o=e.options,i=k.makeArray(t),a=o.length;a--;)((r=o[a]).selected=-1<k.inArray(k.valHooks.option.get(r),i))&&(n=!0);return n||(e.selectedIndex=-1),i}}}}),k.each(["radio","checkbox"],function(){k.valHooks[this]={set:function(e,t){if(Array.isArray(t))return e.checked=-1<k.inArray(k(e).val(),t)}},m.checkOn||(k.valHooks[this].get=function(e){return null===e.getAttribute("value")?"on":e.value})}),C.location),Pn={guid:Date.now()},Hn=/\?/,qn=(k.parseXML=function(e){var t,n;if(!e||"string"!=typeof e)return null;try{t=(new C.DOMParser).parseFromString(e,"text/xml")}catch(e){}return n=t&&t.getElementsByTagName("parsererror")[0],t&&!n||k.error("Invalid XML: "+(n?k.map(n.childNodes,function(e){return e.textContent}).join("\n"):e)),t},/^(?:focusinfocus|focusoutblur)$/),Mn=(k.extend(k.event,{trigger:function(e,t,n,r){var o,i,a,s,l,c,u,f=[n||T],p=z.call(e,"type")?e.type:e,d=z.call(e,"namespace")?e.namespace.split("."):[],h=u=i=n=n||T;if(3!==n.nodeType&&8!==n.nodeType&&!qn.test(p+k.event.triggered)&&(-1<p.indexOf(".")&&(p=(d=p.split(".")).shift(),d.sort()),s=p.indexOf(":")<0&&"on"+p,(e=e[k.expando]?e:new k.Event(p,"object"==typeof e&&e)).isTrigger=r?2:3,e.namespace=d.join("."),e.rnamespace=e.namespace?new RegExp("(^|\\.)"+d.join("\\.(?:.*\\.|)")+"(\\.|$)"):null,e.result=void 0,e.target||(e.target=n),t=null==t?[e]:k.makeArray(t,[e]),c=k.event.special[p]||{},r||!c.trigger||!1!==c.trigger.apply(n,t))){if(!r&&!c.noBubble&&!M(n)){for(a=c.delegateType||p,qn.test(a+p)||(h=h.parentNode);h;h=h.parentNode)f.push(h),i=h;i===(n.ownerDocument||T)&&f.push(i.defaultView||i.parentWindow||C)}for(o=0;(h=f[o++])&&!e.isPropagationStopped();)u=h,e.type=1<o?a:c.bindType||p,(l=(v.get(h,"events")||Object.create(null))[e.type]&&v.get(h,"handle"))&&l.apply(h,t),(l=s&&h[s])&&l.apply&&ct(h)&&(e.result=l.apply(h,t),!1===e.result)&&e.preventDefault();return e.type=p,r||e.isDefaultPrevented()||c._default&&!1!==c._default.apply(f.pop(),t)||!ct(n)||s&&y(n[p])&&!M(n)&&((i=n[s])&&(n[s]=null),k.event.triggered=p,e.isPropagationStopped()&&u.addEventListener(p,In),n[p](),e.isPropagationStopped()&&u.removeEventListener(p,In),k.event.triggered=void 0,i)&&(n[s]=i),e.result}},simulate:function(e,t,n){n=k.extend(new k.Event,n,{type:e,isSimulated:!0});k.event.trigger(n,null,t)}}),k.fn.extend({trigger:function(e,t){return this.each(function(){k.event.trigger(e,t,this)})},triggerHandler:function(e,t){var n=this[0];if(n)return k.event.trigger(e,t,n,!0)}}),/\[\]$/),_n=/\r?\n/g,$n=/^(?:submit|button|image|reset|file)$/i,Bn=/^(?:input|select|textarea|keygen)/i;k.param=function(e,t){function n(e,t){t=y(t)?t():t,o[o.length]=encodeURIComponent(e)+"="+encodeURIComponent(null==t?"":t)}var r,o=[];if(null==e)return"";if(Array.isArray(e)||e.jquery&&!k.isPlainObject(e))k.each(e,function(){n(this.name,this.value)});else for(r in e)!function n(r,e,o,i){if(Array.isArray(e))k.each(e,function(e,t){o||Mn.test(r)?i(r,t):n(r+"["+("object"==typeof t&&null!=t?e:"")+"]",t,o,i)});else if(o||"object"!==Y(e))i(r,e);else for(var t in e)n(r+"["+t+"]",e[t],o,i)}(r,e[r],t,n);return o.join("&")},k.fn.extend({serialize:function(){return k.param(this.serializeArray())},serializeArray:function(){return this.map(function(){var e=k.prop(this,"elements");return e?k.makeArray(e):this}).filter(function(){var e=this.type;return this.name&&!k(this).is(":disabled")&&Bn.test(this.nodeName)&&!$n.test(e)&&(this.checked||!Tt.test(e))}).map(function(e,t){var n=k(this).val();return null==n?null:Array.isArray(n)?k.map(n,function(e){return{name:t.name,value:e.replace(_n,"\r\n")}}):{name:t.name,value:n.replace(_n,"\r\n")}}).get()}});var Rn=/%20/g,Fn=/#.*$/,zn=/([?&])_=[^&]*/,Wn=/^(.*?):[ \t]*([^\r\n]*)$/gm,Un=/^(?:GET|HEAD)$/,Xn=/^\/\//,Gn={},Yn={},Vn="*/".concat("*"),Kn=T.createElement("a");function Qn(i){return function(e,t){"string"!=typeof e&&(t=e,e="*");var n,r=0,o=e.toLowerCase().match(I)||[];if(y(t))for(;n=o[r++];)"+"===n[0]?(n=n.slice(1)||"*",(i[n]=i[n]||[]).unshift(t)):(i[n]=i[n]||[]).push(t)}}function Zn(t,r,o,i){var a={},s=t===Yn;function l(e){var n;return a[e]=!0,k.each(t[e]||[],function(e,t){t=t(r,o,i);return"string"!=typeof t||s||a[t]?s?!(n=t):void 0:(r.dataTypes.unshift(t),l(t),!1)}),n}return l(r.dataTypes[0])||!a["*"]&&l("*")}function Jn(e,t){var n,r,o=k.ajaxSettings.flatOptions||{};for(n in t)void 0!==t[n]&&((o[n]?e:r=r||{})[n]=t[n]);return r&&k.extend(!0,e,r),e}Kn.href=Ln.href,k.extend({active:0,lastModified:{},etag:{},ajaxSettings:{url:Ln.href,type:"GET",isLocal:/^(?:about|app|app-storage|.+-extension|file|res|widget):$/.test(Ln.protocol),global:!0,processData:!0,async:!0,contentType:"application/x-www-form-urlencoded; charset=UTF-8",accepts:{"*":Vn,text:"text/plain",html:"text/html",xml:"application/xml, text/xml",json:"application/json, text/javascript"},contents:{xml:/\bxml\b/,html:/\bhtml/,json:/\bjson\b/},responseFields:{xml:"responseXML",text:"responseText",json:"responseJSON"},converters:{"* text":String,"text html":!0,"text json":JSON.parse,"text xml":k.parseXML},flatOptions:{url:!0,context:!0}},ajaxSetup:function(e,t){return t?Jn(Jn(e,k.ajaxSettings),t):Jn(k.ajaxSettings,e)},ajaxPrefilter:Qn(Gn),ajaxTransport:Qn(Yn),ajax:function(e,t){"object"==typeof e&&(t=e,e=void 0);var l,c,u,n,f,p,d,r,o,h=k.ajaxSetup({},t=t||{}),m=h.context||h,g=h.context&&(m.nodeType||m.jquery)?k(m):k.event,v=k.Deferred(),y=k.Callbacks("once memory"),x=h.statusCode||{},i={},a={},s="canceled",b={readyState:0,getResponseHeader:function(e){var t;if(p){if(!n)for(n={};t=Wn.exec(u);)n[t[1].toLowerCase()+" "]=(n[t[1].toLowerCase()+" "]||[]).concat(t[2]);t=n[e.toLowerCase()+" "]}return null==t?null:t.join(", ")},getAllResponseHeaders:function(){return p?u:null},setRequestHeader:function(e,t){return null==p&&(e=a[e.toLowerCase()]=a[e.toLowerCase()]||e,i[e]=t),this},overrideMimeType:function(e){return null==p&&(h.mimeType=e),this},statusCode:function(e){if(e)if(p)b.always(e[b.status]);else for(var t in e)x[t]=[x[t],e[t]];return this},abort:function(e){e=e||s;return l&&l.abort(e),w(0,e),this}};if(v.promise(b),h.url=((e||h.url||Ln.href)+"").replace(Xn,Ln.protocol+"//"),h.type=t.method||t.type||h.method||h.type,h.dataTypes=(h.dataType||"*").toLowerCase().match(I)||[""],null==h.crossDomain){o=T.createElement("a");try{o.href=h.url,o.href=o.href,h.crossDomain=Kn.protocol+"//"+Kn.host!=o.protocol+"//"+o.host}catch(e){h.crossDomain=!0}}if(h.data&&h.processData&&"string"!=typeof h.data&&(h.data=k.param(h.data,h.traditional)),Zn(Gn,h,t,b),!p){for(r in(d=k.event&&h.global)&&0==k.active++&&k.event.trigger("ajaxStart"),h.type=h.type.toUpperCase(),h.hasContent=!Un.test(h.type),c=h.url.replace(Fn,""),h.hasContent?h.data&&h.processData&&0===(h.contentType||"").indexOf("application/x-www-form-urlencoded")&&(h.data=h.data.replace(Rn,"+")):(o=h.url.slice(c.length),h.data&&(h.processData||"string"==typeof h.data)&&(c+=(Hn.test(c)?"&":"?")+h.data,delete h.data),!1===h.cache&&(c=c.replace(zn,"$1"),o=(Hn.test(c)?"&":"?")+"_="+Pn.guid+++o),h.url=c+o),h.ifModified&&(k.lastModified[c]&&b.setRequestHeader("If-Modified-Since",k.lastModified[c]),k.etag[c])&&b.setRequestHeader("If-None-Match",k.etag[c]),(h.data&&h.hasContent&&!1!==h.contentType||t.contentType)&&b.setRequestHeader("Content-Type",h.contentType),b.setRequestHeader("Accept",h.dataTypes[0]&&h.accepts[h.dataTypes[0]]?h.accepts[h.dataTypes[0]]+("*"!==h.dataTypes[0]?", "+Vn+"; q=0.01":""):h.accepts["*"]),h.headers)b.setRequestHeader(r,h.headers[r]);if(h.beforeSend&&(!1===h.beforeSend.call(m,b,h)||p))return b.abort();if(s="abort",y.add(h.complete),b.done(h.success),b.fail(h.error),l=Zn(Yn,h,t,b)){if(b.readyState=1,d&&g.trigger("ajaxSend",[b,h]),p)return b;h.async&&0<h.timeout&&(f=C.setTimeout(function(){b.abort("timeout")},h.timeout));try{p=!1,l.send(i,w)}catch(e){if(p)throw e;w(-1,e)}}else w(-1,"No Transport")}return b;function w(e,t,n,r){var o,i,a,s=t;p||(p=!0,f&&C.clearTimeout(f),l=void 0,u=r||"",b.readyState=0<e?4:0,r=200<=e&&e<300||304===e,n&&(a=((e,t,n)=>{for(var r,o,i,a,s=e.contents,l=e.dataTypes;"*"===l[0];)l.shift(),void 0===r&&(r=e.mimeType||t.getResponseHeader("Content-Type"));if(r)for(o in s)if(s[o]&&s[o].test(r)){l.unshift(o);break}if(l[0]in n)i=l[0];else{for(o in n){if(!l[0]||e.converters[o+" "+l[0]]){i=o;break}a=a||o}i=i||a}if(i)return i!==l[0]&&l.unshift(i),n[i]})(h,b,n)),!r&&-1<k.inArray("script",h.dataTypes)&&k.inArray("json",h.dataTypes)<0&&(h.converters["text script"]=function(){}),a=((e,t,n,r)=>{var o,i,a,s,l,c={},u=e.dataTypes.slice();if(u[1])for(a in e.converters)c[a.toLowerCase()]=e.converters[a];for(i=u.shift();i;)if(e.responseFields[i]&&(n[e.responseFields[i]]=t),!l&&r&&e.dataFilter&&(t=e.dataFilter(t,e.dataType)),l=i,i=u.shift())if("*"===i)i=l;else if("*"!==l&&l!==i){if(!(a=c[l+" "+i]||c["* "+i]))for(o in c)if((s=o.split(" "))[1]===i&&(a=c[l+" "+s[0]]||c["* "+s[0]])){!0===a?a=c[o]:!0!==c[o]&&(i=s[0],u.unshift(s[1]));break}if(!0!==a)if(a&&e.throws)t=a(t);else try{t=a(t)}catch(e){return{state:"parsererror",error:a?e:"No conversion from "+l+" to "+i}}}return{state:"success",data:t}})(h,a,b,r),r?(h.ifModified&&((n=b.getResponseHeader("Last-Modified"))&&(k.lastModified[c]=n),n=b.getResponseHeader("etag"))&&(k.etag[c]=n),204===e||"HEAD"===h.type?s="nocontent":304===e?s="notmodified":(s=a.state,o=a.data,r=!(i=a.error))):(i=s,!e&&s||(s="error",e<0&&(e=0))),b.status=e,b.statusText=(t||s)+"",r?v.resolveWith(m,[o,s,b]):v.rejectWith(m,[b,s,i]),b.statusCode(x),x=void 0,d&&g.trigger(r?"ajaxSuccess":"ajaxError",[b,h,r?o:i]),y.fireWith(m,[b,s]),d&&(g.trigger("ajaxComplete",[b,h]),--k.active||k.event.trigger("ajaxStop")))}},getJSON:function(e,t,n){return k.get(e,t,n,"json")},getScript:function(e,t){return k.get(e,void 0,t,"script")}}),k.each(["get","post"],function(e,o){k[o]=function(e,t,n,r){return y(t)&&(r=r||n,n=t,t=void 0),k.ajax(k.extend({url:e,type:o,dataType:r,data:t,success:n},k.isPlainObject(e)&&e))}}),k.ajaxPrefilter(function(e){for(var t in e.headers)"content-type"===t.toLowerCase()&&(e.contentType=e.headers[t]||"")}),k._evalUrl=function(e,t,n){return k.ajax({url:e,type:"GET",dataType:"script",cache:!0,async:!1,global:!1,converters:{"text script":function(){}},dataFilter:function(e){k.globalEval(e,t,n)}})},k.fn.extend({wrapAll:function(e){return this[0]&&(y(e)&&(e=e.call(this[0])),e=k(e,this[0].ownerDocument).eq(0).clone(!0),this[0].parentNode&&e.insertBefore(this[0]),e.map(function(){for(var e=this;e.firstElementChild;)e=e.firstElementChild;return e}).append(this)),this},wrapInner:function(n){return y(n)?this.each(function(e){k(this).wrapInner(n.call(this,e))}):this.each(function(){var e=k(this),t=e.contents();t.length?t.wrapAll(n):e.append(n)})},wrap:function(t){var n=y(t);return this.each(function(e){k(this).wrapAll(n?t.call(this,e):t)})},unwrap:function(e){return this.parent(e).not("body").each(function(){k(this).replaceWith(this.childNodes)}),this}}),k.expr.pseudos.hidden=function(e){return!k.expr.pseudos.visible(e)},k.expr.pseudos.visible=function(e){return!!(e.offsetWidth||e.offsetHeight||e.getClientRects().length)},k.ajaxSettings.xhr=function(){try{return new C.XMLHttpRequest}catch(e){}};var er={0:200,1223:204},tr=k.ajaxSettings.xhr();m.cors=!!tr&&"withCredentials"in tr,m.ajax=tr=!!tr,k.ajaxTransport(function(o){var i,a;if(m.cors||tr&&!o.crossDomain)return{send:function(e,t){var n,r=o.xhr();if(r.open(o.type,o.url,o.async,o.username,o.password),o.xhrFields)for(n in o.xhrFields)r[n]=o.xhrFields[n];for(n in o.mimeType&&r.overrideMimeType&&r.overrideMimeType(o.mimeType),o.crossDomain||e["X-Requested-With"]||(e["X-Requested-With"]="XMLHttpRequest"),e)r.setRequestHeader(n,e[n]);i=function(e){return function(){i&&(i=a=r.onload=r.onerror=r.onabort=r.ontimeout=r.onreadystatechange=null,"abort"===e?r.abort():"error"===e?"number"!=typeof r.status?t(0,"error"):t(r.status,r.statusText):t(er[r.status]||r.status,r.statusText,"text"!==(r.responseType||"text")||"string"!=typeof r.responseText?{binary:r.response}:{text:r.responseText},r.getAllResponseHeaders()))}},r.onload=i(),a=r.onerror=r.ontimeout=i("error"),void 0!==r.onabort?r.onabort=a:r.onreadystatechange=function(){4===r.readyState&&C.setTimeout(function(){i&&a()})},i=i("abort");try{r.send(o.hasContent&&o.data||null)}catch(e){if(i)throw e}},abort:function(){i&&i()}}}),k.ajaxPrefilter(function(e){e.crossDomain&&(e.contents.script=!1)}),k.ajaxSetup({accepts:{script:"text/javascript, application/javascript, application/ecmascript, application/x-ecmascript"},contents:{script:/\b(?:java|ecma)script\b/},converters:{"text script":function(e){return k.globalEval(e),e}}}),k.ajaxPrefilter("script",function(e){void 0===e.cache&&(e.cache=!1),e.crossDomain&&(e.type="GET")}),k.ajaxTransport("script",function(n){var r,o;if(n.crossDomain||n.scriptAttrs)return{send:function(e,t){r=k("<script>").attr(n.scriptAttrs||{}).prop({charset:n.scriptCharset,src:n.url}).on("load error",o=function(e){r.remove(),o=null,e&&t("error"===e.type?404:200,e.type)}),T.head.appendChild(r[0])},abort:function(){o&&o()}}});var nr=[],rr=/(=)\?(?=&|$)|\?\?/,or=(k.ajaxSetup({jsonp:"callback",jsonpCallback:function(){var e=nr.pop()||k.expando+"_"+Pn.guid++;return this[e]=!0,e}}),k.ajaxPrefilter("json jsonp",function(e,t,n){var r,o,i,a=!1!==e.jsonp&&(rr.test(e.url)?"url":"string"==typeof e.data&&0===(e.contentType||"").indexOf("application/x-www-form-urlencoded")&&rr.test(e.data)&&"data");if(a||"jsonp"===e.dataTypes[0])return r=e.jsonpCallback=y(e.jsonpCallback)?e.jsonpCallback():e.jsonpCallback,a?e[a]=e[a].replace(rr,"$1"+r):!1!==e.jsonp&&(e.url+=(Hn.test(e.url)?"&":"?")+e.jsonp+"="+r),e.converters["script json"]=function(){return i||k.error(r+" was not called"),i[0]},e.dataTypes[0]="json",o=C[r],C[r]=function(){i=arguments},n.always(function(){void 0===o?k(C).removeProp(r):C[r]=o,e[r]&&(e.jsonpCallback=t.jsonpCallback,nr.push(r)),i&&y(o)&&o(i[0]),i=o=void 0}),"script"}),m.createHTMLDocument=((e=T.implementation.createHTMLDocument("").body).innerHTML="<form></form><form></form>",2===e.childNodes.length),k.parseHTML=function(e,t,n){return"string"!=typeof e?[]:("boolean"==typeof t&&(n=t,t=!1),t||(m.createHTMLDocument?((r=(t=T.implementation.createHTMLDocument("")).createElement("base")).href=T.location.href,t.head.appendChild(r)):t=T),r=!n&&[],(n=Ge.exec(e))?[t.createElement(n[1])]:(n=At([e],t,r),r&&r.length&&k(r).remove(),k.merge([],n.childNodes)));var r},k.fn.load=function(e,t,n){var r,o,i,a=this,s=e.indexOf(" ");return-1<s&&(r=An(e.slice(s)),e=e.slice(0,s)),y(t)?(n=t,t=void 0):t&&"object"==typeof t&&(o="POST"),0<a.length&&k.ajax({url:e,type:o||"GET",dataType:"html",data:t}).done(function(e){i=arguments,a.html(r?k("<div>").append(k.parseHTML(e)).find(r):e)}).always(n&&function(e,t){a.each(function(){n.apply(this,i||[e.responseText,t,e])})}),this},k.expr.pseudos.animated=function(t){return k.grep(k.timers,function(e){return t===e.elem}).length},k.offset={setOffset:function(e,t,n){var r,o,i,a,s=k.css(e,"position"),l=k(e),c={};"static"===s&&(e.style.position="relative"),i=l.offset(),r=k.css(e,"top"),a=k.css(e,"left"),s=("absolute"===s||"fixed"===s)&&-1<(r+a).indexOf("auto")?(o=(s=l.position()).top,s.left):(o=parseFloat(r)||0,parseFloat(a)||0),null!=(t=y(t)?t.call(e,n,k.extend({},i)):t).top&&(c.top=t.top-i.top+o),null!=t.left&&(c.left=t.left-i.left+s),"using"in t?t.using.call(e,c):l.css(c)}},k.fn.extend({offset:function(t){var e,n;return arguments.length?void 0===t?this:this.each(function(e){k.offset.setOffset(this,t,e)}):(n=this[0])?n.getClientRects().length?(e=n.getBoundingClientRect(),n=n.ownerDocument.defaultView,{top:e.top+n.pageYOffset,left:e.left+n.pageXOffset}):{top:0,left:0}:void 0},position:function(){if(this[0]){var e,t,n,r=this[0],o={top:0,left:0};if("fixed"===k.css(r,"position"))t=r.getBoundingClientRect();else{for(t=this.offset(),n=r.ownerDocument,e=r.offsetParent||n.documentElement;e&&(e===n.body||e===n.documentElement)&&"static"===k.css(e,"position");)e=e.parentNode;e&&e!==r&&1===e.nodeType&&((o=k(e).offset()).top+=k.css(e,"borderTopWidth",!0),o.left+=k.css(e,"borderLeftWidth",!0))}return{top:t.top-o.top-k.css(r,"marginTop",!0),left:t.left-o.left-k.css(r,"marginLeft",!0)}}},offsetParent:function(){return this.map(function(){for(var e=this.offsetParent;e&&"static"===k.css(e,"position");)e=e.offsetParent;return e||vt})}}),k.each({scrollLeft:"pageXOffset",scrollTop:"pageYOffset"},function(t,o){var i="pageYOffset"===o;k.fn[t]=function(e){return u(this,function(e,t,n){var r;if(M(e)?r=e:9===e.nodeType&&(r=e.defaultView),void 0===n)return r?r[o]:e[t];r?r.scrollTo(i?r.pageXOffset:n,i?n:r.pageYOffset):e[t]=n},t,e,arguments.length)}}),k.each(["top","left"],function(e,n){k.cssHooks[n]=nn(m.pixelPosition,function(e,t){if(t)return t=tn(e,n),Zt.test(t)?k(e).position()[n]+"px":t})}),k.each({Height:"height",Width:"width"},function(a,s){k.each({padding:"inner"+a,content:s,"":"outer"+a},function(r,i){k.fn[i]=function(e,t){var n=arguments.length&&(r||"boolean"!=typeof e),o=r||(!0===e||!0===t?"margin":"border");return u(this,function(e,t,n){var r;return M(e)?0===i.indexOf("outer")?e["inner"+a]:e.document.documentElement["client"+a]:9===e.nodeType?(r=e.documentElement,Math.max(e.body["scroll"+a],r["scroll"+a],e.body["offset"+a],r["offset"+a],r["client"+a])):void 0===n?k.css(e,t,o):k.style(e,t,n,o)},s,n?e:void 0,n)}})}),k.each(["ajaxStart","ajaxStop","ajaxComplete","ajaxError","ajaxSuccess","ajaxSend"],function(e,t){k.fn[t]=function(e){return this.on(t,e)}}),k.fn.extend({bind:function(e,t,n){return this.on(e,null,t,n)},unbind:function(e,t){return this.off(e,null,t)},delegate:function(e,t,n,r){return this.on(t,e,n,r)},undelegate:function(e,t,n){return 1===arguments.length?this.off(e,"**"):this.off(t,e||"**",n)},hover:function(e,t){return this.on("mouseenter",e).on("mouseleave",t||e)}}),k.each("blur focus focusin focusout resize scroll click dblclick mousedown mouseup mousemove mouseover mouseout mouseenter mouseleave change select submit keydown keypress keyup contextmenu".split(" "),function(e,n){k.fn[n]=function(e,t){return 0<arguments.length?this.on(n,null,e,t):this.trigger(n)}}),/^[\s\uFEFF\xA0]+|([^\s\uFEFF\xA0])[\s\uFEFF\xA0]+$/g),ir=(k.proxy=function(e,t){var n,r;if("string"==typeof t&&(r=e[t],t=e,e=r),y(e))return n=s.call(arguments,2),(r=function(){return e.apply(t||this,n.concat(s.call(arguments)))}).guid=e.guid=e.guid||k.guid++,r},k.holdReady=function(e){e?k.readyWait++:k.ready(!0)},k.isArray=Array.isArray,k.parseJSON=JSON.parse,k.nodeName=x,k.isFunction=y,k.isWindow=M,k.camelCase=O,k.type=Y,k.now=Date.now,k.isNumeric=function(e){var t=k.type(e);return("number"===t||"string"===t)&&!isNaN(e-parseFloat(e))},k.trim=function(e){return null==e?"":(e+"").replace(or,"$1")},"function"==typeof define&&define.amd&&define("jquery",[],function(){return k}),C.jQuery),ar=C.$;return k.noConflict=function(e){return C.$===k&&(C.$=ar),e&&C.jQuery===k&&(C.jQuery=ir),k},void 0===q&&(C.jQuery=C.$=k),k}),function(e){"object"==typeof exports&&"undefined"!=typeof module?module.exports=e():"function"==typeof define&&define.amd?define([],e):("undefined"!=typeof window?window:"undefined"!=typeof global?global:"undefined"!=typeof self?self:this).fitvids=e()}(function(){return function r(o,i,a){function s(n,e){if(!i[n]){if(!o[n]){var t="function"==typeof require&&require;if(!e&&t)return t(n,!0);if(l)return l(n,!0);e=new Error("Cannot find module '"+n+"'");throw e.code="MODULE_NOT_FOUND",e}t=i[n]={exports:{}};o[n][0].call(t.exports,function(e){var t=o[n][1][e];return s(t||e)},t,t.exports,r,o,i,a)}return i[n].exports}for(var l="function"==typeof require&&require,e=0;e<a.length;e++)s(a[e]);return s}({1:[function(e,t,n){var i=['iframe[src*="player.vimeo.com"]','iframe[src*="youtube.com"]','iframe[src*="youtube-nocookie.com"]','iframe[src*="kickstarter.com"][src*="video.html"]',"object"];function a(e,t){return"string"==typeof e&&(t=e,e=document),Array.prototype.slice.call(e.querySelectorAll(t))}function s(e){var t;return"string"==typeof e?e.split(",").map(r).filter(l):"[object Array]"===Object.prototype.toString.call(e)?(t=e.map(s).filter(l),[].concat.apply([],t)):e||[]}function l(e){return 0<e.length}function r(e){return e.replace(/^\s+|\s+$/g,"")}t.exports=function(e,t){t=t||{},o=e=e||"body","[object Object]"===Object.prototype.toString.call(o)&&(t=e,e="body"),t.ignore=t.ignore||"",t.players=t.players||"";var r,n,o=a(e);l(o)&&(document.getElementById("fit-vids-style")||(document.head||document.getElementsByTagName("head")[0]).appendChild(((e=document.createElement("div")).innerHTML='<p>x</p><style id="fit-vids-style">.fluid-width-video-wrapper{width:100%;position:relative;padding:0;}.fluid-width-video-wrapper iframe,.fluid-width-video-wrapper object,.fluid-width-video-wrapper embed {position:absolute;top:0;left:0;width:100%;height:100%;}</style>',e.childNodes[1])),e=s(t.players),t=s(t.ignore),r=0<t.length?t.join():null,l(n=i.concat(e).join()))&&o.forEach(function(e){a(e,n).forEach(function(e){var t,n;r&&e.matches(r)||/fluid-width-video-wrapper/.test((e=e).parentNode.className)||(n=parseInt(e.getAttribute("width"),10),t=parseInt(e.getAttribute("height"),10),n=isNaN(n)?e.clientWidth:n,t=(isNaN(t)?e.clientHeight:t)/n,e.removeAttribute("width"),e.removeAttribute("height"),n=document.createElement("div"),e.parentNode.insertBefore(n,e),n.className="fluid-width-video-wrapper",n.style.paddingTop=100*t+"%",n.appendChild(e))})})}},{}]},{},[1])(1)}),(e=>{"function"==typeof define&&define.amd?define(["jquery"],e):"object"==typeof exports?e(require("jquery")):e(window.jQuery||window.Zepto)})(function(c){function e(){}function u(e,t){h.ev.on("mfp"+e+k,t)}function f(e,t,n,r){var o=document.createElement("div");return o.className="mfp-"+e,n&&(o.innerHTML=n),r?t&&t.appendChild(o):(o=c(o),t&&o.appendTo(t)),o}function p(e,t){h.ev.triggerHandler("mfp"+e,t),h.st.callbacks&&(e=e.charAt(0).toLowerCase()+e.slice(1),h.st.callbacks[e])&&h.st.callbacks[e].apply(h,Array.isArray(t)?t:[t])}function d(e){return e===n&&h.currTemplate.closeBtn||(h.currTemplate.closeBtn=c(h.st.closeMarkup.replace("%title%",h.st.tClose)),n=e),h.currTemplate.closeBtn}function i(){c.magnificPopup.instance||((h=new e).init(),c.magnificPopup.instance=h)}function a(){y&&(v.after(y.addClass(l)).detach(),y=null)}function o(){x&&c(document.body).removeClass(x)}function t(){o(),h.req&&h.req.abort()}var h,r,m,s,g,n,l,v,y,x,b,w="Close",q="BeforeClose",C="MarkupParse",T="Open",k=".mfp",S="mfp-ready",M="mfp-removing",E="mfp-prevent-close",j=!!window.jQuery,A=c(window),D=(c.magnificPopup={instance:null,proto:e.prototype={constructor:e,init:function(){var e=navigator.appVersion;h.isLowIE=h.isIE8=document.all&&!document.addEventListener,h.isAndroid=/android/gi.test(e),h.isIOS=/iphone|ipad|ipod/gi.test(e),h.supportsTransition=(()=>{var e=document.createElement("p").style,t=["ms","O","Moz","Webkit"];if(void 0!==e.transition)return!0;for(;t.length;)if(t.pop()+"Transition"in e)return!0;return!1})(),h.probablyMobile=h.isAndroid||h.isIOS||/(Opera Mini)|Kindle|webOS|BlackBerry|(Opera Mobi)|(Windows Phone)|IEMobile/i.test(navigator.userAgent),m=c(document),h.popupsCache={}},open:function(e){if(!1===e.isObj){h.items=e.items.toArray(),h.index=0;for(var t,n=e.items,r=0;r<n.length;r++)if(((t=n[r]).parsed?t.el[0]:t)===e.el[0]){h.index=r;break}}else h.items=Array.isArray(e.items)?e.items:[e.items],h.index=e.index||0;if(!h.isOpen){h.types=[],g="",e.mainEl&&e.mainEl.length?h.ev=e.mainEl.eq(0):h.ev=m,e.key?(h.popupsCache[e.key]||(h.popupsCache[e.key]={}),h.currTemplate=h.popupsCache[e.key]):h.currTemplate={},h.st=c.extend(!0,{},c.magnificPopup.defaults,e),h.fixedContentPos="auto"===h.st.fixedContentPos?!h.probablyMobile:h.st.fixedContentPos,h.st.modal&&(h.st.closeOnContentClick=!1,h.st.closeOnBgClick=!1,h.st.showCloseBtn=!1,h.st.enableEscapeKey=!1),h.bgOverlay||(h.bgOverlay=f("bg").on("click"+k,function(){h.close()}),h.wrap=f("wrap").attr("tabindex",-1).on("click"+k,function(e){h._checkIfClose(e.target)&&h.close()}),h.container=f("container",h.wrap)),h.contentContainer=f("content"),h.st.preloader&&(h.preloader=f("preloader",h.container,h.st.tLoading));for(var o=c.magnificPopup.modules,r=0;r<o.length;r++){var i=(i=o[r]).charAt(0).toUpperCase()+i.slice(1);h["init"+i].call(h)}p("BeforeOpen"),h.st.showCloseBtn&&(h.st.closeBtnInside?(u(C,function(e,t,n,r){n.close_replaceWith=d(r.type)}),g+=" mfp-close-btn-in"):h.wrap.append(d())),h.st.alignTop&&(g+=" mfp-align-top"),h.fixedContentPos?h.wrap.css({overflow:h.st.overflowY,overflowX:"hidden",overflowY:h.st.overflowY}):h.wrap.css({top:A.scrollTop(),position:"absolute"}),!1!==h.st.fixedBgPos&&("auto"!==h.st.fixedBgPos||h.fixedContentPos)||h.bgOverlay.css({height:m.height(),position:"absolute"}),h.st.enableEscapeKey&&m.on("keyup"+k,function(e){27===e.keyCode&&h.close()}),A.on("resize"+k,function(){h.updateSize()}),h.st.closeOnContentClick||(g+=" mfp-auto-cursor"),g&&h.wrap.addClass(g);var a=h.wH=A.height(),s={},l=(h.fixedContentPos&&h._hasScrollBar(a)&&(l=h._getScrollbarSize())&&(s.marginRight=l),h.fixedContentPos&&(h.isIE7?c("body, html").css("overflow","hidden"):s.overflow="hidden"),h.st.mainClass);return h.isIE7&&(l+=" mfp-ie7"),l&&h._addClassToMFP(l),h.updateItemHTML(),p("BuildControls"),c("html").css(s),h.bgOverlay.add(h.wrap).prependTo(h.st.prependTo||c(document.body)),h._lastFocusedEl=document.activeElement,setTimeout(function(){h.content?(h._addClassToMFP(S),h._setFocus()):h.bgOverlay.addClass(S),m.on("focusin"+k,h._onFocusIn)},16),h.isOpen=!0,h.updateSize(a),p(T),e}h.updateItemHTML()},close:function(){h.isOpen&&(p(q),h.isOpen=!1,h.st.removalDelay&&!h.isLowIE&&h.supportsTransition?(h._addClassToMFP(M),setTimeout(function(){h._close()},h.st.removalDelay)):h._close())},_close:function(){p(w);var e=M+" "+S+" ";h.bgOverlay.detach(),h.wrap.detach(),h.container.empty(),h.st.mainClass&&(e+=h.st.mainClass+" "),h._removeClassFromMFP(e),h.fixedContentPos&&(e={marginRight:""},h.isIE7?c("body, html").css("overflow",""):e.overflow="",c("html").css(e)),m.off("keyup.mfp focusin"+k),h.ev.off(k),h.wrap.attr("class","mfp-wrap").removeAttr("style"),h.bgOverlay.attr("class","mfp-bg"),h.container.attr("class","mfp-container"),!h.st.showCloseBtn||h.st.closeBtnInside&&!0!==h.currTemplate[h.currItem.type]||h.currTemplate.closeBtn&&h.currTemplate.closeBtn.detach(),h.st.autoFocusLast&&h._lastFocusedEl&&c(h._lastFocusedEl).trigger("focus"),h.currItem=null,h.content=null,h.currTemplate=null,h.prevHeight=0,p("AfterClose")},updateSize:function(e){var t;h.isIOS?(t=document.documentElement.clientWidth/window.innerWidth,h.wrap.css("height",t*=window.innerHeight),h.wH=t):h.wH=e||A.height(),h.fixedContentPos||h.wrap.css("height",h.wH),p("Resize")},updateItemHTML:function(){var e=h.items[h.index],t=(h.contentContainer.detach(),h.content&&h.content.detach(),(e=e.parsed?e:h.parseEl(h.index)).type),n=(p("BeforeChange",[h.currItem?h.currItem.type:"",t]),h.currItem=e,h.currTemplate[t]||(p("FirstMarkupParse",n=!!h.st[t]&&h.st[t].markup),h.currTemplate[t]=!n||c(n)),s&&s!==e.type&&h.container.removeClass("mfp-"+s+"-holder"),h["get"+t.charAt(0).toUpperCase()+t.slice(1)](e,h.currTemplate[t]));h.appendContent(n,t),e.preloaded=!0,p("Change",e),s=e.type,h.container.prepend(h.contentContainer),p("AfterChange")},appendContent:function(e,t){(h.content=e)?h.st.showCloseBtn&&h.st.closeBtnInside&&!0===h.currTemplate[t]?h.content.find(".mfp-close").length||h.content.append(d()):h.content=e:h.content="",p("BeforeAppend"),h.container.addClass("mfp-"+t+"-holder"),h.contentContainer.append(h.content)},parseEl:function(e){var t,n=h.items[e];if((n=n.tagName?{el:c(n)}:(t=n.type,{data:n,src:n.src})).el){for(var r=h.types,o=0;o<r.length;o++)if(n.el.hasClass("mfp-"+r[o])){t=r[o];break}n.src=n.el.attr("data-mfp-src"),n.src||(n.src=n.el.attr("href"))}return n.type=t||h.st.type||"inline",n.index=e,n.parsed=!0,h.items[e]=n,p("ElementParse",n),h.items[e]},addGroup:function(t,n){function e(e){e.mfpEl=this,h._openClick(e,t,n)}var r="click.magnificPopup";(n=n||{}).mainEl=t,n.items?(n.isObj=!0,t.off(r).on(r,e)):(n.isObj=!1,n.delegate?t.off(r).on(r,n.delegate,e):(n.items=t).off(r).on(r,e))},_openClick:function(e,t,n){var r=(void 0!==n.midClick?n:c.magnificPopup.defaults).midClick;if(r||!(2===e.which||e.ctrlKey||e.metaKey||e.altKey||e.shiftKey)){if(r=(void 0!==n.disableOn?n:c.magnificPopup.defaults).disableOn)if("function"==typeof r){if(!r.call(h))return!0}else if(A.width()<r)return!0;e.type&&(e.preventDefault(),h.isOpen)&&e.stopPropagation(),n.el=c(e.mfpEl),n.delegate&&(n.items=t.find(n.delegate)),h.open(n)}},updateStatus:function(e,t){var n;h.preloader&&(r!==e&&h.container.removeClass("mfp-s-"+r),p("UpdateStatus",n={status:e,text:t=t||"loading"!==e?t:h.st.tLoading}),e=n.status,t=n.text,h.st.allowHTMLInStatusIndicator?h.preloader.html(t):h.preloader.text(t),h.preloader.find("a").on("click",function(e){e.stopImmediatePropagation()}),h.container.addClass("mfp-s-"+e),r=e)},_checkIfClose:function(e){if(!c(e).closest("."+E).length){var t=h.st.closeOnContentClick,n=h.st.closeOnBgClick;if(t&&n)return!0;if(!h.content||c(e).closest(".mfp-close").length||h.preloader&&e===h.preloader[0])return!0;if(e===h.content[0]||c.contains(h.content[0],e)){if(t)return!0}else if(n&&c.contains(document,e))return!0;return!1}},_addClassToMFP:function(e){h.bgOverlay.addClass(e),h.wrap.addClass(e)},_removeClassFromMFP:function(e){this.bgOverlay.removeClass(e),h.wrap.removeClass(e)},_hasScrollBar:function(e){return(h.isIE7?m.height():document.body.scrollHeight)>(e||A.height())},_setFocus:function(){(h.st.focus?h.content.find(h.st.focus).eq(0):h.wrap).trigger("focus")},_onFocusIn:function(e){if(e.target!==h.wrap[0]&&!c.contains(h.wrap[0],e.target))return h._setFocus(),!1},_parseMarkup:function(o,e,t){var i;t.data&&(e=c.extend(t.data,e)),p(C,[o,e,t]),c.each(e,function(e,t){if(void 0===t||!1===t)return!0;var n,r;1<(i=e.split("_")).length?0<(n=o.find(k+"-"+i[0])).length&&("replaceWith"===(r=i[1])?n[0]!==t[0]&&n.replaceWith(t):"img"===r?n.is("img")?n.attr("src",t):n.replaceWith(c("<img>").attr("src",t).attr("class",n.attr("class"))):n.attr(i[1],t)):h.st.allowHTMLInTemplate?o.find(k+"-"+e).html(t):o.find(k+"-"+e).text(t)})},_getScrollbarSize:function(){var e;return void 0===h.scrollbarSize&&((e=document.createElement("div")).style.cssText="width: 99px; height: 99px; overflow: scroll; position: absolute; top: -9999px;",document.body.appendChild(e),h.scrollbarSize=e.offsetWidth-e.clientWidth,document.body.removeChild(e)),h.scrollbarSize}},modules:[],open:function(e,t){return i(),(e=e?c.extend(!0,{},e):{}).isObj=!0,e.index=t||0,this.instance.open(e)},close:function(){return c.magnificPopup.instance&&c.magnificPopup.instance.close()},registerModule:function(e,t){t.options&&(c.magnificPopup.defaults[e]=t.options),c.extend(this.proto,t.proto),this.modules.push(e)},defaults:{disableOn:0,key:null,midClick:!1,mainClass:"",preloader:!0,focus:"",closeOnContentClick:!1,closeOnBgClick:!0,closeBtnInside:!0,showCloseBtn:!0,enableEscapeKey:!0,modal:!1,alignTop:!1,removalDelay:0,prependTo:null,fixedContentPos:"auto",fixedBgPos:"auto",overflowY:"auto",closeMarkup:'<button title="%title%" type="button" class="mfp-close">&#215;</button>',tClose:"Close (Esc)",tLoading:"Loading...",autoFocusLast:!0,allowHTMLInStatusIndicator:!1,allowHTMLInTemplate:!1}},c.fn.magnificPopup=function(e){i();var t,n,r,o=c(this);return"string"==typeof e?"open"===e?(t=j?o.data("magnificPopup"):o[0].magnificPopup,n=parseInt(arguments[1],10)||0,r=t.items?t.items[n]:(r=o,(r=t.delegate?r.find(t.delegate):r).eq(n)),h._openClick({mfpEl:r},o,t)):h.isOpen&&h[e].apply(h,Array.prototype.slice.call(arguments,1)):(e=c.extend(!0,{},e),j?o.data("magnificPopup",e):o[0].magnificPopup=e,h.addGroup(o,e)),o},"inline"),N=(c.magnificPopup.registerModule(D,{options:{hiddenClass:"hide",markup:"",tNotFound:"Content not found"},proto:{initInline:function(){h.types.push(D),u(w+"."+D,function(){a()})},getInline:function(e,t){var n,r,o;return a(),e.src?(n=h.st.inline,(r=c(e.src)).length?((o=r[0].parentNode)&&o.tagName&&(v||(l=n.hiddenClass,v=f(l),l="mfp-"+l),y=r.after(v).detach().removeClass(l)),h.updateStatus("ready")):(h.updateStatus("error",n.tNotFound),r=c("<div>")),e.inlineElement=r):(h.updateStatus("ready"),h._parseMarkup(t,{},e),t)}}}),"ajax");function I(e){var t;h.currTemplate[P]&&(t=h.currTemplate[P].find("iframe")).length&&(e||(t[0].src="//about:blank"),h.isIE8)&&t.css("display",e?"block":"none")}function O(e){var t=h.items.length;return t-1<e?e-t:e<0?t+e:e}function _(e,t,n){return e.replace(/%curr%/gi,t+1).replace(/%total%/gi,n)}c.magnificPopup.registerModule(N,{options:{settings:null,cursor:"mfp-ajax-cur",tError:"The content could not be loaded."},proto:{initAjax:function(){h.types.push(N),x=h.st.ajax.cursor,u(w+"."+N,t),u("BeforeChange."+N,t)},getAjax:function(r){x&&c(document.body).addClass(x),h.updateStatus("loading");var e=c.extend({url:r.src,success:function(e,t,n){p("ParseAjax",e={data:e,xhr:n}),h.appendContent(c(e.data),N),r.finished=!0,o(),h._setFocus(),setTimeout(function(){h.wrap.addClass(S)},16),h.updateStatus("ready"),p("AjaxContentAdded")},error:function(){o(),r.finished=r.loadError=!0,h.updateStatus("error",h.st.ajax.tError.replace("%url%",r.src))}},h.st.ajax.settings);return h.req=c.ajax(e),""}}}),c.magnificPopup.registerModule("image",{options:{markup:'<div class="mfp-figure"><div class="mfp-close"></div><figure><div class="mfp-img"></div><figcaption><div class="mfp-bottom-bar"><div class="mfp-title"></div><div class="mfp-counter"></div></div></figcaption></figure></div>',cursor:"mfp-zoom-out-cur",titleSrc:"title",verticalFit:!0,tError:"The image could not be loaded."},proto:{initImage:function(){var e=h.st.image,t=".image";h.types.push("image"),u(T+t,function(){"image"===h.currItem.type&&e.cursor&&c(document.body).addClass(e.cursor)}),u(w+t,function(){e.cursor&&c(document.body).removeClass(e.cursor),A.off("resize"+k)}),u("Resize"+t,h.resizeImage),h.isLowIE&&u("AfterChange",h.resizeImage)},resizeImage:function(){var e,t=h.currItem;t&&t.img&&h.st.image.verticalFit&&(e=0,h.isLowIE&&(e=parseInt(t.img.css("padding-top"),10)+parseInt(t.img.css("padding-bottom"),10)),t.img.css("max-height",h.wH-e))},_onImageHasSize:function(e){e.img&&(e.hasSize=!0,b&&clearInterval(b),e.isCheckingImgSize=!1,p("ImageHasSize",e),e.imgHidden)&&(h.content&&h.content.removeClass("mfp-loading"),e.imgHidden=!1)},findImageSize:function(n){var r=0,o=n.img[0];!function e(t){b&&clearInterval(b),b=setInterval(function(){0<o.naturalWidth?h._onImageHasSize(n):(200<r&&clearInterval(b),3==++r?e(10):40===r?e(50):100===r&&e(500))},t)}(1)},getImage:function(t,e){function n(){t&&(t.img.off(".mfploader"),t===h.currItem&&(h._onImageHasSize(t),h.updateStatus("error",i.tError.replace("%url%",t.src))),t.hasSize=!0,t.loaded=!0,t.loadError=!0)}var r,o=0,i=h.st.image,a=e.find(".mfp-img");return a.length&&((r=document.createElement("img")).className="mfp-img",t.el&&t.el.find("img").length&&(r.alt=t.el.find("img").attr("alt")),t.img=c(r).on("load.mfploader",function e(){t&&(t.img[0].complete?(t.img.off(".mfploader"),t===h.currItem&&(h._onImageHasSize(t),h.updateStatus("ready")),t.hasSize=!0,t.loaded=!0,p("ImageLoadComplete")):++o<200?setTimeout(e,100):n())}).on("error.mfploader",n),r.src=t.src,a.is("img")&&(t.img=t.img.clone()),0<(r=t.img[0]).naturalWidth?t.hasSize=!0:r.width||(t.hasSize=!1)),h._parseMarkup(e,{title:(e=>{if(e.data&&void 0!==e.data.title)return e.data.title;var t=h.st.image.titleSrc;if(t){if("function"==typeof t)return t.call(h,e);if(e.el)return e.el.attr(t)||""}return""})(t),img_replaceWith:t.img},t),h.resizeImage(),t.hasSize?(b&&clearInterval(b),t.loadError?(e.addClass("mfp-loading"),h.updateStatus("error",i.tError.replace("%url%",t.src))):(e.removeClass("mfp-loading"),h.updateStatus("ready"))):(h.updateStatus("loading"),t.loading=!0,t.hasSize||(t.imgHidden=!0,e.addClass("mfp-loading"),h.findImageSize(t))),e}}}),c.magnificPopup.registerModule("zoom",{options:{enabled:!1,easing:"ease-in-out",duration:300,opener:function(e){return e.is("img")?e:e.find("img")}},proto:{initZoom:function(){var e,t,n,r,o,i,a=h.st.zoom,s=".zoom";a.enabled&&h.supportsTransition&&(t=a.duration,n=function(e){var e=e.clone().removeAttr("style").removeAttr("class").addClass("mfp-animated-image"),t="all "+a.duration/1e3+"s "+a.easing,n={position:"fixed",zIndex:9999,left:0,top:0,"-webkit-backface-visibility":"hidden"},r="transition";return n["-webkit-"+r]=n["-moz-"+r]=n["-o-"+r]=n[r]=t,e.css(n),e},r=function(){h.content.css("visibility","visible")},u("BuildControls"+s,function(){h._allowZoom()&&(clearTimeout(o),h.content.css("visibility","hidden"),(e=h._getItemToZoom())?((i=n(e)).css(h._getOffset()),h.wrap.append(i),o=setTimeout(function(){i.css(h._getOffset(!0)),o=setTimeout(function(){r(),setTimeout(function(){i.remove(),e=i=null,p("ZoomAnimationEnded")},16)},t)},16)):r())}),u(q+s,function(){if(h._allowZoom()){if(clearTimeout(o),h.st.removalDelay=t,!e){if(!(e=h._getItemToZoom()))return;i=n(e)}i.css(h._getOffset(!0)),h.wrap.append(i),h.content.css("visibility","hidden"),setTimeout(function(){i.css(h._getOffset())},16)}}),u(w+s,function(){h._allowZoom()&&(r(),i&&i.remove(),e=null)}))},_allowZoom:function(){return"image"===h.currItem.type},_getItemToZoom:function(){return!!h.currItem.hasSize&&h.currItem.img},_getOffset:function(e){var t=(e=e?h.currItem.img:h.st.zoom.opener(h.currItem.el||h.currItem)).offset(),n=parseInt(e.css("padding-top"),10),r=parseInt(e.css("padding-bottom"),10),e=(t.top-=c(window).scrollTop()-n,{width:e.width(),height:(j?e.innerHeight():e[0].offsetHeight)-r-n});return(L=void 0===L?void 0!==document.createElement("p").style.MozTransform:L)?e["-moz-transform"]=e.transform="translate("+t.left+"px,"+t.top+"px)":(e.left=t.left,e.top=t.top),e}}});var L,P="iframe",H=(c.magnificPopup.registerModule(P,{options:{markup:'<div class="mfp-iframe-scaler"><div class="mfp-close"></div><iframe class="mfp-iframe" src="//about:blank" frameborder="0" allowfullscreen></iframe></div>',srcAction:"iframe_src",patterns:{youtube:{index:"youtube.com",id:"v=",src:"//www.youtube.com/embed/%id%?autoplay=1"},vimeo:{index:"vimeo.com/",id:"/",src:"//player.vimeo.com/video/%id%?autoplay=1"},gmaps:{index:"//maps.google.",src:"%id%&output=embed"}}},proto:{initIframe:function(){h.types.push(P),u("BeforeChange",function(e,t,n){t!==n&&(t===P?I():n===P&&I(!0))}),u(w+"."+P,function(){I()})},getIframe:function(e,t){var n=e.src,r=h.st.iframe,o=(c.each(r.patterns,function(){if(-1<n.indexOf(this.index))return this.id&&(n="string"==typeof this.id?n.substr(n.lastIndexOf(this.id)+this.id.length,n.length):this.id.call(this,n)),n=this.src.replace("%id%",n),!1}),{});return r.srcAction&&(o[r.srcAction]=n),h._parseMarkup(t,o,e),h.updateStatus("ready"),t}}}),c.magnificPopup.registerModule("gallery",{options:{enabled:!1,arrowMarkup:'<button title="%title%" type="button" class="mfp-arrow mfp-arrow-%dir%"></button>',preload:[0,2],navigateByImgClick:!0,arrows:!0,tPrev:"Previous (Left arrow key)",tNext:"Next (Right arrow key)",tCounter:"%curr% of %total%",langDir:null,loop:!0},proto:{initGallery:function(){var i=h.st.gallery,e=".mfp-gallery";if(h.direction=!0,!i||!i.enabled)return!1;i.langDir||(i.langDir=document.dir||"ltr"),g+=" mfp-gallery",u(T+e,function(){i.navigateByImgClick&&h.wrap.on("click"+e,".mfp-img",function(){if(1<h.items.length)return h.next(),!1}),m.on("keydown"+e,function(e){37===e.keyCode?"rtl"===i.langDir?h.next():h.prev():39===e.keyCode&&("rtl"===i.langDir?h.prev():h.next())}),h.updateGalleryButtons()}),u("UpdateStatus"+e,function(){h.updateGalleryButtons()}),u("UpdateStatus"+e,function(e,t){t.text&&(t.text=_(t.text,h.currItem.index,h.items.length))}),u(C+e,function(e,t,n,r){var o=h.items.length;n.counter=1<o?_(i.tCounter,r.index,o):""}),u("BuildControls"+e,function(){var e,t,n,r,o;1<h.items.length&&i.arrows&&!h.arrowLeft&&(t="rtl"===i.langDir?(r=i.tNext,e=i.tPrev,o="next","prev"):(r=i.tPrev,e=i.tNext,o="prev","next"),n=i.arrowMarkup,r=h.arrowLeft=c(n.replace(/%title%/gi,r).replace(/%action%/gi,o).replace(/%dir%/gi,"left")).addClass(E),o=h.arrowRight=c(n.replace(/%title%/gi,e).replace(/%action%/gi,t).replace(/%dir%/gi,"right")).addClass(E),"rtl"===i.langDir?(h.arrowNext=r,h.arrowPrev=o):(h.arrowNext=o,h.arrowPrev=r),r.on("click",function(){"rtl"===i.langDir?h.next():h.prev()}),o.on("click",function(){"rtl"===i.langDir?h.prev():h.next()}),h.container.append(r.add(o)))}),u("Change"+e,function(){h._preloadTimeout&&clearTimeout(h._preloadTimeout),h._preloadTimeout=setTimeout(function(){h.preloadNearbyImages(),h._preloadTimeout=null},16)}),u(w+e,function(){m.off(e),h.wrap.off("click"+e),h.arrowRight=h.arrowLeft=null})},next:function(){var e=O(h.index+1);if(!h.st.gallery.loop&&0===e)return!1;h.direction=!0,h.index=e,h.updateItemHTML()},prev:function(){var e=h.index-1;if(!h.st.gallery.loop&&e<0)return!1;h.direction=!1,h.index=O(e),h.updateItemHTML()},goTo:function(e){h.direction=e>=h.index,h.index=e,h.updateItemHTML()},preloadNearbyImages:function(){for(var e=h.st.gallery.preload,t=Math.min(e[0],h.items.length),n=Math.min(e[1],h.items.length),r=1;r<=(h.direction?n:t);r++)h._preloadItem(h.index+r);for(r=1;r<=(h.direction?t:n);r++)h._preloadItem(h.index-r)},_preloadItem:function(e){var t;e=O(e),h.items[e].preloaded||(p("LazyLoad",t=(t=h.items[e]).parsed?t:h.parseEl(e)),"image"===t.type&&(t.img=c('<img class="mfp-img" />').on("load.mfploader",function(){t.hasSize=!0}).on("error.mfploader",function(){t.hasSize=!0,t.loadError=!0,p("LazyLoadError",t)}).attr("src",t.src)),t.preloaded=!0)},updateGalleryButtons:function(){h.st.gallery.loop||"object"!=typeof h.arrowPrev||null===h.arrowPrev||(0===h.index?h.arrowPrev.hide():h.arrowPrev.show(),h.index===h.items.length-1?h.arrowNext.hide():h.arrowNext.show())}}}),"retina");c.magnificPopup.registerModule(H,{options:{replaceSrc:function(e){return e.src.replace(/\.\w+$/,function(e){return"@2x"+e})},ratio:1},proto:{initRetina:function(){var n,r;1<window.devicePixelRatio&&(n=h.st.retina,r=n.ratio,1<(r=isNaN(r)?r():r))&&(u("ImageHasSize."+H,function(e,t){t.img.css({"max-width":t.img[0].naturalWidth/r,width:"100%"})}),u("ElementParse."+H,function(e,t){t.src=n.replaceSrc(t,r)}))}}}),i()}),(e=>{"function"==typeof define&&define.amd?define(["jquery"],e):e("object"==typeof module&&module.exports?require("jquery"):jQuery)})(function(m){function t(e){var t=[],n=e.dir&&"left"===e.dir?"scrollLeft":"scrollTop";return this.each(function(){var e=m(this);if(this!==document&&this!==window)return!document.scrollingElement||this!==document.documentElement&&this!==document.body?void(0<e[n]()?t.push(this):(e[n](1),0<e[n]()&&t.push(this),e[n](0))):(t.push(document.scrollingElement),!1)}),t.length||this.each(function(){(t=this===document.documentElement&&"smooth"===m(this).css("scrollBehavior")?[this]:t).length||"BODY"!==this.nodeName||(t=[this])}),t="first"===e.el&&1<t.length?[t[0]]:t}function c(e){var t={relative:""},n="string"==typeof e&&r.exec(e);return"number"==typeof e?t.px=e:n&&(t.relative=n[1],t.px=parseFloat(n[2])||0),t}function u(e){var t=m(e.scrollTarget);e.autoFocus&&t.length&&(t[0].focus(),t.is(document.activeElement)||(t.prop({tabIndex:-1}),t[0].focus())),e.afterScroll.call(e.link,e)}var f={},r=/^([\-\+]=)(\d+)/;m.fn.extend({scrollable:function(e){e=t.call(this,{dir:e});return this.pushStack(e)},firstScrollable:function(e){e=t.call(this,{el:"first",dir:e});return this.pushStack(e)},smoothScroll:function(e,t){var h,n;return"options"===(e=e||{})?t?this.each(function(){var e=m(this),e=m.extend(e.data("ssOpts")||{},t);m(this).data("ssOpts",e)}):this.first().data("ssOpts"):(h=m.extend({},m.fn.smoothScroll.defaults,e),n=function(e){function t(e){return e.replace(/(:|\.|\/)/g,"\\$1")}var n=this,r=m(this),o=m.extend({},h,r.data("ssOpts")||{}),i=h.exclude,a=o.excludeWithin,s=0,l=0,c=!0,u={},f=m.smoothScroll.filterPath(location.pathname),p=m.smoothScroll.filterPath(n.pathname),d=location.hostname===n.hostname||!n.hostname,p=o.scrollTarget||p===f,f=t(n.hash);if(f&&!m(f).length&&(c=!1),o.scrollTarget||d&&p&&f){for(;c&&s<i.length;)r.is(t(i[s++]))&&(c=!1);for(;c&&l<a.length;)r.closest(a[l++]).length&&(c=!1)}else c=!1;c&&(o.preventDefault&&e.preventDefault(),m.extend(u,o,{scrollTarget:o.scrollTarget||f,link:n}),m.smoothScroll(u))},null!==e.delegateSelector?this.off("click.smoothscroll",e.delegateSelector).on("click.smoothscroll",e.delegateSelector,n):this.off("click.smoothscroll").on("click.smoothscroll",n),this)}});m.smoothScroll=function(e,t){if("options"===e&&"object"==typeof t)return m.extend(f,t);var n,r,o=c(e),i=0,a="offset",s={},l={};o.px?n=m.extend({link:null},m.fn.smoothScroll.defaults,f):((n=m.extend({link:null},m.fn.smoothScroll.defaults,e||{},f)).scrollElement&&"static"===n.scrollElement.css(a="position")&&n.scrollElement.css("position","relative"),t&&(o=c(t))),e="left"===n.direction?"scrollLeft":"scrollTop",n.scrollElement?(r=n.scrollElement,o.px||/^(?:HTML|BODY)$/.test(r[0].nodeName)||(i=r[e]())):r=m("html, body").firstScrollable(n.direction),n.beforeScroll.call(r,n),t=o.px?o:{relative:"",px:m(n.scrollTarget)[a]()&&m(n.scrollTarget)[a]()[n.direction]||0},s[e]=t.relative+(t.px+i+n.offset),l={duration:o="auto"===(o=n.speed)?Math.abs(s[e]-r[e]())/n.autoCoefficient:o,easing:n.easing,complete:function(){u(n)}},n.step&&(l.step=n.step),r.length?r.stop().animate(s,l):u(n)},m.smoothScroll.version="2.2.0",m.smoothScroll.filterPath=function(e){return(e=e||"").replace(/^\//,"").replace(/(?:index|default).[a-zA-Z]{3,4}$/,"").replace(/\/$/,"")},m.fn.smoothScroll.defaults={exclude:[],excludeWithin:[],offset:0,direction:"top",delegateSelector:null,scrollElement:null,scrollTarget:null,autoFocus:!1,beforeScroll:function(){},afterScroll:function(){},easing:"swing",speed:400,autoCoefficient:2,preventDefault:!0}});var $nav=$("#site-nav"),$btn=$("#site-nav button"),$vlinks=$("#site-nav .visible-links"),$hlinks=$("#site-nav .hidden-links"),breaks=[];function updateNav(){var e=$btn.hasClass("hidden")?$nav.width():$nav.width()-$btn.width()-30;$vlinks.width()>e?(breaks.push($vlinks.width()),$vlinks.children("*:not(.masthead__menu-item--lg)").last().prependTo($hlinks),$btn.hasClass("hidden")&&$btn.removeClass("hidden")):(e>breaks[breaks.length-1]&&($hlinks.children().first().appendTo($vlinks),breaks.pop()),breaks.length<1&&($btn.addClass("hidden"),$hlinks.addClass("hidden"))),$btn.attr("count",breaks.length),$vlinks.width()>e&&updateNav()}$(window).resize(function(){updateNav()}),$btn.on("click",function(){$hlinks.toggleClass("hidden"),$(this).toggleClass("close")}),updateNav(),$(document).ready(function(){scssLarge=925;function e(){$("body").css("margin-bottom",$(".page__footer").outerHeight(!0))}var t=!1,n=(e(),$(window).resize(function(){t=!0}),setInterval(function(){t&&(t=!1,e())},250),fitvids(),$(".author__urls-wrapper button").on("click",function(){$(".author__urls").fadeToggle("fast",function(){}),$(".author__urls-wrapper button").toggleClass("open")}),jQuery(window).on("resize",function(){"none"==$(".author__urls.social-icons").css("display")&&$(window).width()>=scssLarge&&$(".author__urls").css("display","block")}),$("a").smoothScroll({offset:-65}),$("a[href$='.jpg'],a[href$='.jpeg'],a[href$='.JPG'],a[href$='.png'],a[href$='.gif']").addClass("image-popup"),$(".image-popup").magnificPopup({type:"image",tLoading:"Loading image #%curr%...",gallery:{enabled:!0,navigateByImgClick:!0,preload:[0,1]},image:{tError:'<a href="%url%">Image #%curr%</a> could not be loaded.'},removalDelay:500,mainClass:"mfp-zoom-in",callbacks:{beforeOpen:function(){this.st.image.markup=this.st.image.markup.replace("mfp-figure","mfp-figure mfp-with-anim")}},closeOnContentClick:!0,midClick:!0}),document.querySelectorAll(".page__inner-wrap"));let r=new IntersectionObserver(e=>{e.forEach(e=>{e.isIntersecting&&e.target.classList.add("is-visible")})},{threshold:.1});n.forEach(e=>{r.observe(e)})});