{"cells": [{"cell_type": "code", "execution_count": null, "id": "01f0c5c2-619a-48e0-bb7c-e5e0be009f0e", "metadata": {}, "outputs": [], "source": ["orcid = '0000-0000-0000-0000' # Fill your orcid here"]}, {"cell_type": "code", "execution_count": null, "id": "2fe4bc4e-4574-4322-8b18-0c4d33a749fa", "metadata": {}, "outputs": [], "source": ["import requests"]}, {"cell_type": "markdown", "id": "44a8b6cd-4034-4fc4-85a8-e3431dc564f1", "metadata": {}, "source": ["We use the `/works` api to list all works related to the orcid. This gives a summary of all works, so citation information is not included. We collect the `put-code` of all works to retrieve the citation information later."]}, {"cell_type": "code", "execution_count": null, "id": "3b04331e-4149-4ca3-a0aa-89e3ba892723", "metadata": {}, "outputs": [], "source": ["response = requests.get('https://pub.orcid.org/v3.0/{}/works'.format(orcid),\n", "                        headers={\"Accept\": \"application/orcid+json\" })\n", "record = response.json()"]}, {"cell_type": "code", "execution_count": null, "id": "16f7c42d-623b-421a-8d87-bbb389313e3b", "metadata": {"scrolled": true}, "outputs": [], "source": ["put_codes = []\n", "for work in record['group']:\n", "    put_code = work['work-summary'][0]['put-code']\n", "    put_codes.append(put_code)\n", "put_code = put_codes[0]"]}, {"cell_type": "markdown", "id": "25e5d2aa-5233-486e-abce-a0d07a36c5ce", "metadata": {}, "source": ["We use the `/<orcid>/work/<put-code>` endpoint to retrieve the citation information for each record."]}, {"cell_type": "code", "execution_count": null, "id": "dd797a16-0d91-4271-8e1e-b82579a07e45", "metadata": {}, "outputs": [], "source": ["citations = []\n", "for put_code in put_codes:\n", "    response = requests.get('https://pub.orcid.org/v3.0/{}/work/{}'.format(orcid, put_code),\n", "                            headers={\"Accept\": \"application/orcid+json\" })\n", "    work = response.json()\n", "    if work['citation'] is not None:\n", "        citations.append(work['citation']['citation-value'])"]}, {"cell_type": "code", "execution_count": null, "id": "ad763df9-261f-41f3-bc32-00921d0a4e11", "metadata": {}, "outputs": [], "source": ["with open('output.bib', 'w') as bibfile:\n", "    for citation in citations:\n", "        bibfile.write(citation)\n", "        bibfile.write('\\n')"]}], "metadata": {"kernelspec": {"display_name": "Python 3 (ipykernel)", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.12.6"}}, "nbformat": 4, "nbformat_minor": 5}