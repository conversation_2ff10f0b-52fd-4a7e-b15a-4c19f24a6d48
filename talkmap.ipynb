{"cells": [{"cell_type": "markdown", "metadata": {}, "source": ["# Leaflet cluster map of talk locations\n", "\n", "Run this from the _talks/ directory, which contains .md files of all your talks. This scrapes the location YAML field from each .md file, geolocates it with geopy/Nominatim, and uses the getorg library to output data, HTML, and Javascript for a standalone cluster map."]}, {"cell_type": "code", "execution_count": 1, "metadata": {"collapsed": false}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Requirement already up-to-date: getorg in /home/<USER>/anaconda3/lib/python3.5/site-packages\n", "Requirement already up-to-date: geopy in /home/<USER>/.local/lib/python3.5/site-packages (from getorg)\n", "Requirement already up-to-date: retrying in /home/<USER>/.local/lib/python3.5/site-packages (from getorg)\n", "Requirement already up-to-date: pygithub in /home/<USER>/anaconda3/lib/python3.5/site-packages (from getorg)\n", "Requirement already up-to-date: six>=1.7.0 in /home/<USER>/.local/lib/python3.5/site-packages (from retrying->getorg)\n", "IPywidgets and ipyleaflet support enabled.\n"]}], "source": ["!pip install getorg --upgrade\n", "import glob\n", "import getorg\n", "from geopy import Nominatim"]}, {"cell_type": "code", "execution_count": 2, "metadata": {"collapsed": false}, "outputs": [], "source": ["g = glob.glob(\"*.md\")"]}, {"cell_type": "code", "execution_count": 3, "metadata": {"collapsed": true}, "outputs": [], "source": ["geocoder = Nominatim()\n", "location_dict = {}\n", "location = \"\"\n", "permalink = \"\"\n", "title = \"\""]}, {"cell_type": "code", "execution_count": 4, "metadata": {"collapsed": false}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Berkeley CA, USA \n", " Berkeley, Alameda County, California, United States of America\n", "Los Angeles, CA \n", " LA, Los Angeles County, California, United States of America\n", "London, UK \n", " London, Greater London, England, UK\n", "San Francisco, California \n", " SF, California, United States of America\n"]}], "source": ["\n", "for file in g:\n", "    with open(file, 'r') as f:\n", "        lines = f.read()\n", "        if lines.find('location: \"') > 1:\n", "            loc_start = lines.find('location: \"') + 11\n", "            lines_trim = lines[loc_start:]\n", "            loc_end = lines_trim.find('\"')\n", "            location = lines_trim[:loc_end]\n", "                            \n", "           \n", "        location_dict[location] = geocoder.geocode(location)\n", "        print(location, \"\\n\", location_dict[location])\n"]}, {"cell_type": "code", "execution_count": 5, "metadata": {"collapsed": false}, "outputs": [{"data": {"text/plain": ["'Written map to ../talkmap/'"]}, "execution_count": 5, "metadata": {}, "output_type": "execute_result"}], "source": ["m = getorg.orgmap.create_map_obj()\n", "getorg.orgmap.output_html_cluster_map(location_dict, folder_name=\"../talkmap\", hashed_usernames=False)"]}, {"cell_type": "code", "execution_count": null, "metadata": {"collapsed": true}, "outputs": [], "source": []}], "metadata": {"anaconda-cloud": {}, "kernelspec": {"display_name": "Python [Root]", "language": "python", "name": "Python [Root]"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.5.2"}}, "nbformat": 4, "nbformat_minor": 0}