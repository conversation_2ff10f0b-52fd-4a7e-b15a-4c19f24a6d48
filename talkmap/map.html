
    <!DOCTYPE html>
    <html>
    <head>
    	<title>Leaflet debug page</title>

    	<link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/leaflet/1.0.0-beta.2/leaflet.css" />
    	<script src="https://cdnjs.cloudflare.com/ajax/libs/leaflet/1.0.0-beta.2/leaflet.js"></script>
    	<meta name="viewport" content="width=device-width, initial-scale=1.0">
    	<link rel="stylesheet" href="leaflet_dist/screen.css" />

    	<link rel="stylesheet" href="leaflet_dist/MarkerCluster.css" />
    	<link rel="stylesheet" href="leaflet_dist/MarkerCluster.Default.css" />
    	<script src="leaflet_dist/leaflet.markercluster-src.js"></script>
    	<script src="org-locations.js"></script>

    </head>
    <body>

    	<div id="map"></div>
    	<span>Mouse over a cluster to see the bounds of its children and click a cluster to zoom to those bounds</span>
    	<script type="text/javascript">
    		var tiles = L.tileLayer('http://server.arcgisonline.com/ArcGIS/rest/services/World_Street_Map/MapServer/tile/{z}/{y}/{x}', {
              maxZoom: 18,
              attribution: 'Tiles &copy; Esri &mdash; Source: Esri, DeLorme, NAVTEQ, USGS, Intermap, iPC, NRCAN, Esri Japan, METI, Esri China (Hong Kong), Esri (Thailand), TomTom, 2012'
                    }),
    			latlng = L.latLng(30, 10);
    		var map = L.map('map', {center: latlng, zoom: 0.7, layers: [tiles]});
    		var markers = L.markerClusterGroup({
    			showCoverageOnHover: false,
    			maxClusterRadius: 80
    			});
    		for (var i = 0; i < addressPoints.length; i++) {
    			var a = addressPoints[i];
    			var title = a[0];
    			var marker = L.marker(new L.LatLng(a[1], a[2]), { title: title });
    			marker.bindPopup(title);
    			markers.addLayer(marker);
    		}
    		map.addLayer(markers);
    		map.zoomIn();
    	</script>
    </body>
    </html>
    