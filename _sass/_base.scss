@import url('https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&family=JetBrains+Mono:wght@400;500;600&display=swap');

/* ==========================================================================
   BASE ELEMENTS
   ========================================================================== */
html {
  /* sticky footer fix */
  position: relative;
  min-height: 100%;
}

body {
  margin: 0;
  padding: $masthead-height 0 0;
  padding-bottom: 9em;
  color: $text-color;
  font-family: $global-font-family;
  font-weight: $font-weight-normal;
  line-height: 1.6;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;

  &.overflow--hidden {
    /* when primary navigation is visible, the content in the background won't scroll */
    overflow: hidden;
  }
}

h1, h2, h3, h4, h5, h6 {
  margin: 2.5em 0 0.75em;
  line-height: 1.25;
  font-family: $header-font-family;
  font-weight: $font-weight-semibold;
  letter-spacing: -0.025em;
  color: $gray-800;
}

h1 {
  margin-top: 0;
  font-size: $type-size-2;
  font-weight: $font-weight-bold;
  background: $bg-gradient-accent;
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

h2 {
  font-size: $type-size-3;
  font-weight: $font-weight-semibold;
  color: $gray-800;
}

h3 {
  font-size: $type-size-4;
  font-weight: $font-weight-semibold;
  color: $gray-700;
}

h4 {
  font-size: $type-size-5;
  font-weight: $font-weight-medium;
  color: $gray-700;
}

h5 {
  font-size: $type-size-6;
  font-weight: $font-weight-medium;
  color: $gray-600;
}

h6 {
  font-size: $type-size-7;
  font-weight: $font-weight-medium;
  color: $gray-600;
  text-transform: uppercase;
  letter-spacing: 0.05em;
}

small, .small {
  font-size: $type-size-6;
}

p {
  margin-bottom: 1.3em;
}

u,
ins {
  text-decoration: none;
  border-bottom: 1px solid $text-color;
  a {
    color: inherit;
  }
}

del a {
  color: inherit;
}

/* reduce orphans and widows when printing */

p, pre, blockquote, ul, ol, dl, figure, table, fieldset {
  orphans: 3;
  widows: 3;
}

/* abbreviations */

abbr[title],
abbr[data-original-title] {
  text-decoration: none;
  cursor: help;
  border-bottom: 1px dotted $text-color;
}

/* blockquotes */

blockquote {
  margin: 2em 1em 2em 0;
  padding-left: 1em;
  padding-right: 1em;
  font-style: italic;
  border-left: 0.25em solid $primary-color;

  cite {
    font-style: italic;

    &:before {
      content: "\2014";
      padding-right: 5px;
    }
  }
}

/* links */

a {
  color: $link-color;
  text-decoration: none;
  transition: all $duration-normal $ease-out-cubic;
  position: relative;

  &:focus {
    @extend %tab-focus;
  }

  &:hover {
    color: $link-color-hover;
    outline: 0;

    &:not(.btn) {
      &::after {
        transform: scaleX(1);
      }
    }
  }

  &:active {
    outline: 0;
    transform: translateY(1px);
  }

  /* Modern underline effect for text links */
  &:not(.btn):not(.author__urls a) {
    &::after {
      content: '';
      position: absolute;
      bottom: -2px;
      left: 0;
      width: 100%;
      height: 2px;
      background: linear-gradient(90deg, $primary-color, $secondary-color);
      transform: scaleX(0);
      transform-origin: left;
      transition: transform $duration-normal $ease-out-cubic;
    }
  }
}

/* code */

tt, code, kbd, samp, pre {
  font-family: $monospace;
}

pre {
  overflow-x: auto; /* add scrollbars to wide code blocks*/
}

p > code,
a > code,
li > code,
figcaption > code,
td > code {
  padding: $space-1 $space-2;
  font-size: $type-size-7;
  font-family: $monospace;
  font-weight: $font-weight-medium;
  background: $code-background-color;
  border: 1px solid $gray-200;
  border-radius: $border-radius-sm;
  box-shadow: $box-shadow-sm;
  color: $primary-color;

  &:before, &:after {
    letter-spacing: -0.2em;
    content: "\00a0"; /* non-breaking space*/
  }
}

/* horizontal rule */

hr {
  display: block;
  margin: 1em 0;
  border: 0;
  border-top: 1px solid $border-color;
}

/* lists */

ul li,
ol li {
  margin-bottom: 0.5em;
}

li ul,
li ol {
  margin-top: 0.5em;
}

/*
   Media and embeds
   ========================================================================== */

/* Figures and images */

figure {
  display: -webkit-box;
  display: flex;
  -webkit-box-pack: justify;
          justify-content: space-between;
  -webkit-box-align: start;
          align-items: flex-start;
  flex-wrap: wrap;
  margin: 2em 0;

  img,
  iframe,
  .fluid-width-video-wrapper {
    margin-bottom: 1em;
  }

  img {
    width: 100%;
    border-radius: $border-radius;
    -webkit-transition: $global-transition;
    transition: $global-transition;
  }

  > a {
    display: block;
  }

  &.half {
    > a,
    > img {
      @include breakpoint($small) {
        width: calc(50% - 0.5em);
      }
    }

    figcaption {
      width: 100%;
    }
  }

  &.third {
    > a,
    > img {
      @include breakpoint($small) {
        width: calc(33.3333% - 0.5em);
      }
    }

    figcaption {
      width: 100%;
    }
  }
}

/* Figure captions */

figcaption {
  margin-bottom: 0.5em;
  color: mix(#fff, $text-color, 25%);
  font-family: $caption-font-family;
  font-size: $type-size-6;

  a {
    color: inherit;
    text-decoration: none;
    border-bottom: 1px solid $light-gray;
    -webkit-transition: $global-transition;
    transition: $global-transition;

    &:hover {
      color: #000;
      border-bottom-color: #000;
    }
  }
}


/* Fix IE9 SVG bug */

svg:not(:root) {
  overflow: hidden;
}


/*
   Navigation lists
   ========================================================================== */

/**
 * Removes margins, padding, and bullet points from navigation lists
 *
 * Example usage:
 * <nav>
 *    <ul>
 *      <li><a href="#link-1">Link 1</a></li>
 *      <li><a href="#link-2">Link 2</a></li>
 *      <li><a href="#link-3">Link 3</a></li>
 *    </ul>
 *  </nav>
 */

nav {
  ul {
    margin: 0;
    padding: 0;
  }

  li {
    list-style: none;
  }

  a {
    text-decoration: none;
  }

  /* override white-space for nested lists */
  ul li,
  ol li {
    margin-bottom: 0;
  }

  li ul,
  li ol {
    margin-top: 0;
  }
}

/*
   Global animation transition
   ========================================================================== */

b, i, strong, em, blockquote, p, q, span, figure, img, h1, h2, header, input, a, tr, td, form button, input[type="submit"], .btn, .highlight, .archive__item-teaser {
  -webkit-transition: $global-transition;
  transition: $global-transition;
}
