@import url('https://fonts.googleapis.com/css2?family=Inter:wght@400;700&display=swap');

/* ==========================================================================
   BASE ELEMENTS
   ========================================================================== */
html {
  /* sticky footer fix */
  position: relative;
  min-height: 100%;
}

body {
  margin: 0;
  padding: $masthead-height 0 0;
  padding-bottom: 9em;
  color: $text-color;
  font-family: $global-font-family;
  line-height: 1.5;

  &.overflow--hidden {
    /* when primary navigation is visible, the content in the background won't scroll */
    overflow: hidden;
  }
}

h1, h2, h3, h4, h5, h6 {
  margin: 2em 0 0.5em;
  line-height: 1.2;
  font-family: $header-font-family;
  font-weight: bold;
}

h1 {
  margin-top: 0;
  font-size: $type-size-3;
}

h2 {
  font-size: $type-size-4;
}

h3 {
  font-size: $type-size-5;
}

h4 {
  font-size: $type-size-6;
}

h5 {
  font-size: $type-size-6;
}

h6 {
  font-size: $type-size-6;
}

small, .small {
  font-size: $type-size-6;
}

p {
  margin-bottom: 1.3em;
}

u,
ins {
  text-decoration: none;
  border-bottom: 1px solid $text-color;
  a {
    color: inherit;
  }
}

del a {
  color: inherit;
}

/* reduce orphans and widows when printing */

p, pre, blockquote, ul, ol, dl, figure, table, fieldset {
  orphans: 3;
  widows: 3;
}

/* abbreviations */

abbr[title],
abbr[data-original-title] {
  text-decoration: none;
  cursor: help;
  border-bottom: 1px dotted $text-color;
}

/* blockquotes */

blockquote {
  margin: 2em 1em 2em 0;
  padding-left: 1em;
  padding-right: 1em;
  font-style: italic;
  border-left: 0.25em solid $primary-color;

  cite {
    font-style: italic;

    &:before {
      content: "\2014";
      padding-right: 5px;
    }
  }
}

/* links */

a {
  &:focus {
    @extend %tab-focus;
  }

  &:hover,
  &:active {
    outline: 0;
  }
}

/* code */

tt, code, kbd, samp, pre {
  font-family: $monospace;
}

pre {
  overflow-x: auto; /* add scrollbars to wide code blocks*/
}

p > code,
a > code,
li > code,
figcaption > code,
td > code {
  padding-top: 0.1rem;
  padding-bottom: 0.1rem;
  font-size: $type-size-6;
  background: $code-background-color;
  border: 1px solid $lighter-gray;
  border-radius: $border-radius;
  box-shadow: $box-shadow;

  &:before, &:after {
    letter-spacing: -0.2em;
    content: "\00a0"; /* non-breaking space*/
  }
}

/* horizontal rule */

hr {
  display: block;
  margin: 1em 0;
  border: 0;
  border-top: 1px solid $border-color;
}

/* lists */

ul li,
ol li {
  margin-bottom: 0.5em;
}

li ul,
li ol {
  margin-top: 0.5em;
}

/*
   Media and embeds
   ========================================================================== */

/* Figures and images */

figure {
  display: -webkit-box;
  display: flex;
  -webkit-box-pack: justify;
          justify-content: space-between;
  -webkit-box-align: start;
          align-items: flex-start;
  flex-wrap: wrap;
  margin: 2em 0;

  img,
  iframe,
  .fluid-width-video-wrapper {
    margin-bottom: 1em;
  }

  img {
    width: 100%;
    border-radius: $border-radius;
    -webkit-transition: $global-transition;
    transition: $global-transition;
  }

  > a {
    display: block;
  }

  &.half {
    > a,
    > img {
      @include breakpoint($small) {
        width: calc(50% - 0.5em);
      }
    }

    figcaption {
      width: 100%;
    }
  }

  &.third {
    > a,
    > img {
      @include breakpoint($small) {
        width: calc(33.3333% - 0.5em);
      }
    }

    figcaption {
      width: 100%;
    }
  }
}

/* Figure captions */

figcaption {
  margin-bottom: 0.5em;
  color: mix(#fff, $text-color, 25%);
  font-family: $caption-font-family;
  font-size: $type-size-6;

  a {
    color: inherit;
    text-decoration: none;
    border-bottom: 1px solid $light-gray;
    -webkit-transition: $global-transition;
    transition: $global-transition;

    &:hover {
      color: #000;
      border-bottom-color: #000;
    }
  }
}


/* Fix IE9 SVG bug */

svg:not(:root) {
  overflow: hidden;
}


/*
   Navigation lists
   ========================================================================== */

/**
 * Removes margins, padding, and bullet points from navigation lists
 *
 * Example usage:
 * <nav>
 *    <ul>
 *      <li><a href="#link-1">Link 1</a></li>
 *      <li><a href="#link-2">Link 2</a></li>
 *      <li><a href="#link-3">Link 3</a></li>
 *    </ul>
 *  </nav>
 */

nav {
  ul {
    margin: 0;
    padding: 0;
  }

  li {
    list-style: none;
  }

  a {
    text-decoration: none;
  }

  /* override white-space for nested lists */
  ul li,
  ol li {
    margin-bottom: 0;
  }

  li ul,
  li ol {
    margin-top: 0;
  }
}

/*
   Global animation transition
   ========================================================================== */

b, i, strong, em, blockquote, p, q, span, figure, img, h1, h2, header, input, a, tr, td, form button, input[type="submit"], .btn, .highlight, .archive__item-teaser {
  -webkit-transition: $global-transition;
  transition: $global-transition;
}
