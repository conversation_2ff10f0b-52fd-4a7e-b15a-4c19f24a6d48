// Susy Settings
// =============

// Susy Language Defaults
// ----------------------
// - PRIVATE
@include susy-defaults((
  container: auto,
  math: fluid,
  output: float,
  container-position: center,
  gutter-position: after,
  global-box-sizing: content-box,
  debug: (
    image: hide,
    color: rgba(#66f, .25),
    output: background,
    toggle: top right,
  ),
));


// Valid Keyword Values
// --------------------
// - PRIVATE: DONT'T TOUCH
$susy-keywords: (
  container: auto,
  math: static fluid,
  output: isolate float,
  container-position: left center right,
  flow: ltr rtl,
  gutter-position: before after split inside inside-static,
  box-sizing: border-box content-box,
  span: full,
  edge: first alpha last omega full,
  spread: narrow wide wider,
  gutter-override: no-gutters no-gutter,
  role: nest,
  clear: break nobreak,
  debug image: show hide show-columns show-baseline,
  debug output: background overlay,
);


// Parse Susy Keywords and Maps
// ----------------------------
@function parse-settings(
  $short: $susy
) {
  $_return: ();

  @if type-of($short) == map {
    $_return: $short;
  } @else {
    @each $item in $short {
      // strings
      @if type-of($item) == string {
        @each $key, $value in $susy-keywords {
          @if index($value, $item) {
            $_key-value: append($key, $item);
            $_return: _susy-deep-set($_return, $_key-value...);
          }
        }
      // maps
      } @else if type-of($item) == map {
        $_return: map-merge($_return, $item);
      }
    }
  }

  @return $_return;
}


// Parse Columns & Gutters
// -----------------------
@function parse-layout(
  $short
) {
  $_return: ();
  $_columns: ();
  $_gutters: null;

  @if not(unitless(nth(nth($short, 1), 1))) {
    $_gutters: nth($short, 1);
  } @else {
    $_columns: (columns: nth($short, 1));
    $_gutters: if(length($short) > 1, nth($short, 2), $_gutters);
  }

  @if type-of($_gutters) == list and length($_gutters) > 0 {
    $_gutters: (
      gutters: nth($_gutters, 2) / nth($_gutters, 1),
      column-width: nth($_gutters, 1),
    );
  } @else {
    $_gutters: if($_gutters, (gutters: $_gutters), ());
  }

  $_return: map-merge($_return, $_columns);
  $_return: map-merge($_return, $_gutters);

  @return $_return;
}


// Parse Grid/Context
// ------------------
@function parse-grid(
  $short: $susy
) {
  $_return: parse-settings($short);
  $_layout: ();

  @if type-of($short) == map {
    $_return: $short;
  } @else {
    @each $item in $short {
      // number or list
      @if type-of($item) == number or type-of($item) == list {
        @if type-of($item) == list or unitless($item) {
          $_layout: append($_layout, $item);
        } @else {
          $_return: map-merge($_return, (container: $item));
        }
      }
    }

    $_layout: if(length($_layout) > 0, parse-layout($_layout), $_layout);
  }

  @return map-merge($_return, $_layout);
}


// Parse Span
// ----------
@function parse-span(
  $short,
  $key: span
) {
  $_return: ();

  @if type-of($short) == map {
    $_return: $short;
  } @else {
    $_at: index($short, at);

    @if $_at {
      $_loci: $_at + 1;
      $_location: nth($short, $_loci);
      $_return: map-merge($_return, (location: $_location));
      $short: set-nth($short, $_at, null);
      $short: set-nth($short, $_loci, null);
    }

    $_i: 1;
    $_span: ();

    @while $_i <= length($short) {
      $_this: nth($short, $_i);

      @if type-of($_this) == number {
        $_span: append($_span, $_this);
        $short: set-nth($short, $_i, null);
      } @else if $_this == of {
        $short: set-nth($short, $_i, null);
        $_i: length($short) + 1;
      }

      $_i: $_i + 1;
    }

    @if length($_span) > 0 {
      $_span: if(length($_span) == 1, nth($_span, 1), $_span);
      $_return: map-merge($_return, ($key: $_span));
    }

    $_return: map-merge($_return, parse-grid($short));
  }

  @return $_return;
}


// Parse Gutters
// -------------
@function parse-gutters(
  $short: $susy
) {
  $_gutters: parse-span($short, gutter-override);
  $_span: susy-get(gutter-override, $_gutters);

  @if $_span and not(map-get($_gutters, columns)) {
    $_context: ();
    $_new: ();

    @each $item in $_span {
      @if type-of($item) == number and unitless($item) {
        $_context: append($_context, $item);
      } @else {
        $_new: append($_new, $item);
      }
    }

    $_context: parse-grid($_context);
    $_new: if(length($_new) == 0, null, $_new);
    $_new: if(length($_new) == 1, nth($_new, 1), $_new);
    $_new: (gutter-override: if($_new != $_span, $_new, $_span));

    $_gutters: map-merge($_gutters, $_new);
    $_gutters: map-merge($_gutters, $_context);
  }

  @return $_gutters;
}
