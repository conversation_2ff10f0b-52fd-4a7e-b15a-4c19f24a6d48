// Background Grid Syntax
// ======================

$susy-overlay-grid-head-exists: false;


// Show Grid/s
// -----------
// Show grid on any element using either background or overlay.
// - [$grid]  : <settings>
@mixin show-grid(
  $grid: $susy
) {
  $inspect: $grid;
  $_output: debug-get(output, $grid);

  @include susy-inspect(show-grid, $inspect);
  @if $_output == overlay and susy-get(debug image, $grid) != hide {
    @include overlay-grid($grid);
  } @else {
    @include background-grid($grid);
  }
}

@mixin show-grids(
  $grid: $susy
) {
  @include show-grid($grid);
}

// Background Grid
// ---------------
// Show a grid background on any element.
// - [$grid]  : <settings>
@mixin background-grid(
  $grid: $susy
) {
  $inspect  : $grid;
  $_output  : get-background($grid);

  @if length($_output) > 0 {
    $_flow: susy-get(flow, $grid);

    $_image: ();
    @each $name, $layer in map-get($_output, image) {
      $_direction: if($name == baseline, to bottom, to to($_flow));
      $_image: append($_image, linear-gradient($_direction, $layer), comma);
    }
    $_output: map-merge($_output, (image: $_image));

    @include background-grid-output($_output...);
    @include susy-inspect(background-grid, $inspect);
  }
}


// Overlay Grid
// ------------
// Generate an icon to trigger grid-overlays on any given elements.
// $grids...  : <selector> [<settings>] [, <selector>]*
@mixin overlay-grid (
  $grid: $susy
) {
  @if not($susy-overlay-grid-head-exists) {
    @at-root head { @include overlay-head($grid); }
    @at-root head:before { @include overlay-trigger; }
    @at-root head:hover { @include overlay-trigger-hover; }
    $susy-overlay-grid-head-exists: true !global;
  }

  head:hover ~ &,
  head:hover ~ body & {
    position: relative;
    &:before {
      @include grid-overlay-base;
      @include background-grid($grid);
    }
  }
}


// [Private] Overlay Trigger
// -------------------------
@mixin overlay-trigger {
  content: "|||";
  display: block;
  padding: 5px 10px;
  font: {
    family: sans-serif;
    size: 16px;
    weight: bold;
  }
}


// [Private] Overlay Trigger Hover
// -------------------------------
@mixin overlay-trigger-hover {
  background: rgba(white, .5);
  color: red;
}


// [Private] Overlay Head
// ----------------------
// <head> styles to create grid overlay toggle
@mixin overlay-head (
  $grid: $susy
) {
  $_toggle: debug-get(toggle, $grid);
  $_horz: null;
  $_vert: null;

  @each $side in $_toggle {
    $_horz: if($side == left or $side == right, $side, $_horz);
    $_vert: if($side == top or $side == bottom, $side, $_vert);
  }

  display: block;
  position: fixed;
  #{$_horz}: 10px;
  #{$_vert}: 10px;
  z-index: 999;
  color: #333;
  background: rgba(white, .25);
}


// [Private] Grid Overlay Base
// ---------------------------
// Base styles for generating a grid overlay
@mixin grid-overlay-base() {
  position: absolute;
  top: 0;
  left: 0;
  bottom: 0;
  right: 0;
  content: " ";
  z-index: 998;
}


// Get Symmetrical Background
// --------------------------
// - $grid: <map>
@function get-background-sym(
  $grid
) {
  $grid           : parse-grid($grid);
  $_gutters       : susy-get(gutters, $grid);
  $_column-width  : susy-get(column-width, $grid);
  $_math          : susy-get(math, $grid);

  $_color         : debug-get(color);
  $_trans         : transparent;
  $_light         : lighten($_color, 15%);

  $_end           : 1 + $_gutters;
  $_after         : percentage(1/$_end);
  $_stops         : ();
  $_size          : span(1 $grid wide);

  @if is-inside($grid) {
    $_stops: $_color, $_light;
  } @else if is-split($grid) {
    $_split: $_gutters/2;
    $_before: percentage($_split/$_end);
    $_after: percentage((1 + $_split)/$_end);
    $_stops: $_trans $_before, $_color $_before, $_light $_after, $_trans $_after;
  } @else {
    $_stops: $_color, $_light $_after, $_trans $_after;
  }

  @if $_math == static {
    $_size: valid-column-math($_math, $_column-width) * $_end;
  }

  $_output: (
    image: (columns: $_stops),
    size: $_size,
  );

  @return $_output;
}


// Get Asymmetrical Inside
// -----------------------
// - $grid: <settings>
@function get-asym-inside(
  $grid
) {
  $grid     : parse-grid($grid);
  $_columns : susy-get(columns, $grid);

  $_color   : debug-get(color);
  $_light   : lighten($_color, 15%);
  $_stops   : ();

  @for $location from 1 through susy-count($_columns) {
    $this-stop: ();

    @if $location == 1 {
      $this-stop: append($this-stop, $_color, comma);
    } @else {
      $start: parse-span(1 at $location $grid);
      $start: get-isolation($start);
      $this-stop: append($this-stop, $_color $start, comma);
    }

    @if $location == susy-count($_columns) {
      $this-stop: append($this-stop, $_light, comma);
    } @else {
      $_end: parse-span(1 at ($location + 1) $grid);
      $_end: get-isolation($_end);
      $this-stop: append($this-stop, $_light $_end, comma);
    }

    $_stops: join($_stops, $this-stop, comma);
  }

  @return $_stops;
}


// Get Asymmetrical Split
// ----------------------
// - $grid: <settings>
@function get-asym-split(
  $grid
) {
  $grid     : parse-grid($grid);
  $_columns : susy-get(columns, $grid);

  $_color   : debug-get(color);
  $_light   : lighten($_color, 15%);
  $_stops   : ();

  @for $location from 1 through susy-count($_columns) {
    $this-stop: ();

    $start: parse-span(1 at $location $grid);
    $start: get-isolation($start);
    $this-stop: append($this-stop, transparent $start, comma);
    $this-stop: append($this-stop, $_color $start, comma);

    $_end: $start + span(1 at $location $grid);
    $this-stop: append($this-stop, $_light $_end, comma);
    $this-stop: append($this-stop, transparent $_end, comma);

    $_stops: join($_stops, $this-stop, comma);
  }

  @return $_stops;
}


// Get Asymmetrical Outside
// ------------------------
// - $grid: <settings>
@function get-asym-outside(
  $grid
) {
  $grid     : parse-grid($grid);
  $_columns : susy-get(columns, $grid);

  $_color   : debug-get(color);
  $_light   : lighten($_color, 15%);
  $_trans   : transparent;
  $_stops   : ();

  @for $location from 1 through susy-count($_columns) {
    $this-stop: ();

    @if $location == 1 {
      $this-stop: append($this-stop, $_color, comma);
    } @else {
      $start: parse-span(1 at $location $grid);
      $start: get-isolation($start);
      $this-stop: append($this-stop, $_color $start, comma);
    }

    @if $location == susy-count($_columns) {
      $this-stop: append($this-stop, $_light, comma);
    } @else {
      $gutter: get-span-width(first $location $grid);

      $_end: parse-span(1 at ($location + 1) $grid);
      $_end: get-isolation($_end);

      $gutter: $_light $gutter, $_trans $gutter, $_trans $_end;
      $this-stop: join($this-stop, $gutter, comma);
    }

    $_stops: join($_stops, $this-stop, comma);
  }

  @return $_stops;
}


// Get Asymmetrical Background
// ---------------------------
// - $grid: <settings>
@function get-background-asym(
  $grid
) {
  $_stops: ();

  @if is-inside($grid) {
    $_stops: get-asym-inside($grid);
  } @else if is-split($grid) {
    $_stops: get-asym-split($grid);
  } @else {
    $_stops: get-asym-outside($grid);
  }

  @return (image: (columns: $_stops));
}


// Get Background
// --------------
// - $grid: <settings>
@function get-background(
  $grid
) {
  $grid     : parse-grid($grid);
  $_show    : susy-get(debug image, $grid);
  $_return  : ();

  @if $_show and $_show != 'hide' {
    $_columns: susy-get(columns, $grid);

    @if $_show != 'show-baseline' {
      $_sym: is-symmetrical($_columns);
      $_return: if($_sym, get-background-sym($grid), get-background-asym($grid));
      $_return: map-merge($_return, (clip: content-box));
    }

    @if $_show != 'show-columns'
      and global-variable-exists(base-line-height)
      and type-of($base-line-height) == 'number'
      and not unitless($base-line-height) {
      $_color: variable-exists('grid-background-baseline-color');
      $_color: if($_color, $grid-background-baseline-color, #000);

      $_image: map-get($_return, image);
      $_size: map-get($_return, size);
      $_baseline: (baseline: ($_color 1px, transparent 1px));
      $_baseline-size: 100% $base-line-height;

      $_return: map-merge($_return, (
        image: if($_image, map-merge($_image, $_baseline), $_baseline),
        size: if($_size, ($_size, $_baseline-size), $_baseline-size),
      ));

      @if $_show == 'show' {
        $_clip: map-get($_return, clip);
        $_return: map-merge($_return, (clip: join($_clip, border-box, comma)));
      }
    } @else if $_show == 'show-baseline' {
      @warn 'Please provide a $base-line-height with the desired height and units';
    }
  }

  @if map-get($_return, image) {
    $_return: map-merge($_return, (flow: susy-get(flow, $grid)));
  }

  @return $_return;
}


// Get Debug
// ---------
// Return the value of a debug setting
// - $key: <setting>
@function debug-get(
  $key,
  $grid: $susy
) {
  $key: join(debug, $key, space);
  @return susy-get($key, $grid);
}
