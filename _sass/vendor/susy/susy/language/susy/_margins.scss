// Margin Syntax
// =============

// Pre
// ---
// Add spanning-margins before an element.
// - $span  : <span>
@mixin pre(
  $span
) {
  $inspect: $span;
  $span   : map-merge((spread: wide), parse-span($span));
  $flow   : susy-get(flow, $span);
  $split  : if(susy-get(gutter-position, $span) == split, true, false);
  $gutter : gutter($span);
  $span   : span($span);
  $width  : if($split and $gutter, $span + $gutter, $span);

  @include susy-inspect(pre, $inspect);
  @include margin-output($width, null, $flow);
}

// Post
// ----
// Add spanning-margins after an element.
// - $span  : <span>
@mixin post(
  $span
) {
  $inspect  : $span;
  $span     : map-merge((spread: wide), parse-span($span));
  $flow     : susy-get(flow, $span);
  $split    : if(susy-get(gutter-position, $span) == split, true, false);
  $width    : if($split, span($span) + gutter($span), span($span));

  @include susy-inspect(post, $inspect);
  @include margin-output(null, $width, $flow);
}

// Push
// ----
// Simple synonymn for pre.
// - $span  : <span>
@mixin push(
  $span
) {
  @include pre($span);
}

// Pull
// ----
// Add negative spanning-margins before an element.
// - $span  : <span>
@mixin pull(
  $span
) {
  $inspect  : $span;
  $span     : map-merge((spread: wide), parse-span($span));
  $flow     : susy-get(flow, $span);
  $split    : if(susy-get(gutter-position, $span) == split, true, false);
  $width    : if($split, 0 - span($span) + gutter($span), 0 - span($span));

  @include susy-inspect(pull, $inspect);
  @include margin-output($width, null, $flow);
}

// Squish
// ------
// Add spanning-margins before and after an element.
// - $pre     : <span>
// - [$post]  : <span>
@mixin squish(
  $pre,
  $post: false
) {
  $inspect      : ($pre, $post);
  $pre          : map-merge((spread: wide), parse-span($pre));

  @if $post {
    $post: map-merge((spread: wide), parse-span($post));
  } @else {
    $span: susy-get(span, $pre);
    @if length($span) > 1 {
      $pre: map-merge($pre, (span: nth($span, 1)));
      $post: map-merge($pre, (span: nth($span, 2)));
    } @else {
      $post: $pre;
    }
  }

  @include susy-inspect(squish, $inspect);
  @include pre($pre);
  @include post($post);
}
