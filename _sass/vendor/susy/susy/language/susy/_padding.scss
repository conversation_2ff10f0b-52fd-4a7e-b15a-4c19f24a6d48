// Padding Syntax
// ==============

// Prefix
// ------
// Add spanning-padding before an element.
// - $span  : <span>
@mixin prefix(
  $span
) {
  $inspect  : $span;
  $span     : map-merge((spread: wide), parse-span($span));
  $flow     : susy-get(flow, $span);
  $width    : span($span);

  @if is-inside($span) {
    $gutter: gutter($span);
    $width: if($gutter and comparable($width, $gutter), $width + $gutter, $width);
  }

  @include susy-inspect(prefix, $inspect);
  @include padding-output($width, null, $flow);
}

// Suffix
// ------
// Add spanning-padding after an element.
// - $span  : <span>
@mixin suffix(
  $span
) {
  $inspect  : $span;
  $span     : map-merge((spread: wide), parse-span($span));
  $flow     : susy-get(flow, $span);
  $width    : span($span);

  @if is-inside($span) {
    $gutter: gutter($span);
    $width: if($gutter and comparable($width, $gutter), $width + $gutter, $width);
  }

  @include susy-inspect(suffix, $inspect);
  @include padding-output(null, $width, $flow);
}

// Pad
// ---
// Add spanning-padding before and after an element.
// - $pre     : <span>
// - [$post]  : <span>
@mixin pad(
  $pre,
  $post: false
) {
  $inspect  : ($pre, $post);
  $pre      : map-merge((spread: wide), parse-span($pre));

  @if $post {
    $post: map-merge((spread: wide), parse-span($post));
  } @else {
    $span: susy-get(span, $pre);
    @if length($span) > 1 {
      $pre: map-merge($pre, (span: nth($span, 1)));
      $post: map-merge($pre, (span: nth($span, 2)));
    } @else {
      $post: $pre;
    }
  }

  @include susy-inspect(pad, $inspect);
  @include prefix($pre);
  @include suffix($post);

}
