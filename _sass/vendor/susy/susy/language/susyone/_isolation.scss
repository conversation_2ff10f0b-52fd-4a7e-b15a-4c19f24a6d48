// ---------------------------------------------------------------------------
// Isolation

// Isolate the position of a grid element (use in addition to span-columns)
//
// $location  : The grid column to isolate in, relative to the container;
// $context   : [optional] The context (columns spanned by parent).
// $from      : The start direction of your layout (e.g. 'left' for ltr languages)
@mixin isolate(
  $location,
  $context: $total-columns,
  $from: $from-direction,
  $style: fix-static-misalignment()
) {
  $to: opposite-position($from);
  margin-#{$to}: -100%;
  margin-#{$from}: space($location - 1, $context, $style);
}

// Isolate a group of elements in a grid, using nth-child selectors
//
// $columns       : The column-width of each item on the grid;
// $context       : [optional] The context (columns spanned by parent).
// $selector      : [child | of-type | last-of-type ] (default is 'child')
// $from          : The start direction of your layout (e.g. 'left' for ltr languages)
@mixin isolate-grid(
  $columns,
  $context: $total-columns,
  $selector: 'child',
  $from: $from-direction,
  $style: fix-static-misalignment()
) {
  $to: opposite-position($from);
  $location: 1;
  $line: floor($context / $columns);

  @include span-columns($columns, $context, $from: $from, $style: $style);
  margin-#{$to}: -100%;

  @for $item from 1 through $line {
    $nth: '#{$line}n + #{$item}';
    &:#{format-nth($nth,$selector)} {
      margin-#{$from}: space($location - 1, $context, $style);
      @if $location == 1 { clear: $from; }
      @else { clear: none; }

      $location: $location + $columns;
      @if $location > $context { $location: 1; }
    }
  }
}
