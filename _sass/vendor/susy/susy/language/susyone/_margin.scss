// ---------------------------------------------------------------------------
// Margin Mixins

// Apply 'columns' margin before an element to push it along the grid.
//
// $columns : The number of columns to span.
// $context : [optional] The context (columns spanned by parent).
//          : Context is required on any nested elements.
//          : Context MUST NOT be declared on a root element.
// $from    : The start direction of your layout (e.g. 'left' for ltr languages)
// $style   : The container style to use.
@mixin pre(
  $columns,
  $context  : $total-columns,
  $from     : $from-direction,
  $style    : fix-static-misalignment()
) {
  $from     : unquote($from);
  margin-#{$from}: space($columns, $context, $style);
}

// 'push' is a synonymn for 'pre'
@mixin push(
  $columns,
  $context  : $total-columns,
  $from     : $from-direction,
  $style    : fix-static-misalignment()
) {
  $from     : unquote($from);
  @include pre($columns, $context, $from, $style);
}

// Apply negative 'columns' margin before an element to pull it along the grid.
//
// $columns : The number of columns to span.
// $context : [optional] The context (columns spanned by parent).
//          : Context is required on any nested elements.
//          : Context MUST NOT be declared on a root element.
// $from    : The start direction of your layout (e.g. 'left' for ltr languages)
// $style   : The container style to use.
@mixin pull(
  $columns,
  $context  : $total-columns,
  $from     : $from-direction,
  $style    : fix-static-misalignment()
) {
  $from     : unquote($from);
  margin-#{$from}: 0 - space($columns, $context, $style);
}

// Apply 'columns' margin after an element to contain it in a grid.
//
// $columns : The number of columns to span.
// $context : [optional] The context (columns spanned by parent).
//          : Context is required on any nested elements.
//          : Context MUST NOT be declared on a root element.
// $from    : The start direction of your layout (e.g. 'left' for ltr languages)
// $style   : The container style to use.
@mixin post(
  $columns,
  $context  : $total-columns,
  $from     : $from-direction,
  $style    : fix-static-misalignment()
) {
  $from     : unquote($from);
  $to : opposite-position($from);
  margin-#{$to}: space($columns, $context, $style);
}

// Apply 'columns' before and/or after an element to contain it on a grid.
//
// $pre     : The number of columns to add as margin before.
// $post    : The number of columns to add as margin after.
// $context : [optional] The context (columns spanned by parent).
//          : Context is required on any nested elements.
//          : Context MUST NOT be declared on a root element.
// $from    : The start direction of your layout (e.g. 'left' for ltr languages)
// $style   : The container style to use.
@mixin squish(
  $pre      : false,
  $post     : false,
  $context  : $total-columns,
  $from     : $from-direction,
  $style    : fix-static-misalignment()
) {
  $from     : unquote($from);
  @if $pre {
    @include pre($pre, $context, $from, $style)
  }
  @if $post {
    @include post($post, $context, $from, $style)
  }
}
