//////////////////////////////
// Has Setting
//////////////////////////////
@function breakpoint-has($setting) {
  @if map-has-key($breakpoint, $setting) {
    @return true;
  }
  @else {
    @return false;
  }
}

//////////////////////////////
// Get Settings
//////////////////////////////
@function breakpoint-get($setting) {
  @if breakpoint-has($setting) {
    @return map-get($breakpoint, $setting);
  }
  @else {
    @return map-get($Breakpoint-Settings, $setting);
  }
}

//////////////////////////////
// Set Settings
//////////////////////////////
@function breakpoint-set($setting, $value) {
  @if (str-index($setting, '-') or str-index($setting, '_')) and str-index($setting, ' ') == null {
    @warn "Words in Breakpoint settings should be separated by spaces, not dashes or underscores. Please replace dashes and underscores between words with spaces. Settings will not work as expected until changed.";
  }
  $breakpoint: map-merge($breakpoint, ($setting: $value)) !global;
  @return true;
}

@mixin breakpoint-change($setting, $value) {
  $breakpoint-change: breakpoint-set($setting, $value);
}

@mixin breakpoint-set($setting, $value) {
  @include breakpoint-change($setting, $value);
}

@mixin bkpt-change($setting, $value) {
  @include breakpoint-change($setting, $value);
}
@mixin bkpt-set($setting, $value) {
  @include breakpoint-change($setting, $value);
}

//////////////////////////////
// Remove Setting
//////////////////////////////
@function breakpoint-reset($settings...) {
  @if length($settings) == 1 {
    $settings: nth($settings, 1);
  }

  @each $setting in $settings {
    $breakpoint: map-remove($breakpoint, $setting) !global;
  }
  @return true;
}

@mixin breakpoint-reset($settings...) {
  $breakpoint-reset: breakpoint-reset($settings);
}

@mixin bkpt-reset($settings...) {
  $breakpoint-reset: breakpoint-reset($settings);
}