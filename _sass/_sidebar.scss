/* ==========================================================================
   SIDEBAR
   ========================================================================== */

/*
   Default
   ========================================================================== */

.sidebar {
  -webkit-transform: translate3d(0, 0 , 0);
          transform: translate3d(0, 0 , 0);

  @include clearfix();
  margin-bottom: 1em;

  @media screen and (min-width: $sidebar-screen-min-width) {
    height: 100vh;
    overflow-y: auto;               // Add scrollbar if the sidebar is too long
    position: fixed;
    padding-top: $masthead-height;
  }

  @include breakpoint($large) {
    @include span(2 of 12);
    opacity: 1;
    -webkit-transition: opacity 0.2s ease-in-out;
            transition: opacity 0.2s ease-in-out;

    &:hover {
      opacity: 1;
    }
  }

  @include breakpoint($x-large) {
    max-width: $sidebar-link-max-width;
    padding-right: 0;
  }

  h2, h3, h4, h5, h6 {
    margin-bottom: 0;
    font-family: $sans-serif-narrow;
  }

  h3, h4 {
    font-size: $type-size-5;
  }

  p, li {
    font-family: $sans-serif;
    font-size: 0.875em; /* Previously $type-size-6 */
    line-height: 1.5;
  }

  img {
    width: 100%;
  }
}

.sidebar__right {
  margin-bottom: 1em;

  @include breakpoint($large) {
    position: relative;
    float: right;
    width: $right-sidebar-width-narrow;
    margin-left: span(0.5 of 12);
    z-index: 10;
  }

  @include breakpoint($x-large) {
    width: $right-sidebar-width;
  }
}

/*
   Author profile and links
   ========================================================================== */

.author__avatar {
  display: table-cell;
  vertical-align: top;
  width: 36px;
  // set width only, for non-square avatars
  // height: 36px;

  @include breakpoint($large) {
    display: block;
    width: auto;
    height: auto;
    text-align: center; /* Center the inline-block image */
  }

  img {
    max-width: 175px;
    border-radius: 50%;

    @include breakpoint($large) {
      padding: 5px;
      border: 1px solid $border-color;
      display: block; /* Ensure block context for margin auto */
      margin-left: auto;
      margin-right: auto;
    }
  }
}


.author__content {
  display: table-cell;
  vertical-align: top;
  padding-left: 15px;
  padding-right: 25px;
  line-height: 1.2;

  @include breakpoint($large) {
    display: block;
    width: 100%;
    padding-left: 0;
    padding-right: 0;
    text-align: center;
  }
}

.author__name {
  margin: 0;

  @include breakpoint($large) {
    margin-top: 10px;
    margin-bottom: 10px;
  }
}
.sidebar .author__name {
  font-family: $sans-serif;
  font-size: 1.375em; /* Previously $type-size-4 */
  font-weight: bold;
}

.sidebar .author__desktop {
  display: none;
  @media screen and (min-width: 1024px) {
    display: block;
  }
}

.author__pronouns {
  margin: 0;

  @include breakpoint($large) {
    margin-top: 10px;
    margin-bottom: 10px;
  }
}

.author__bio {
  margin: 0;

  @include breakpoint($large) {
    margin-top: 10px;
    margin-bottom: 20px;
  }
}

.author__urls-wrapper {
  position: relative;
  display: table-cell;
  vertical-align: middle;
  font-family: $sans-serif;
  z-index: 10;
  position: relative;
  cursor: pointer;

  li:last-child {
    a {
      margin-bottom: 0;
    }
  }

  @include breakpoint($large) {
    display: block;
  }

  button {
    margin-bottom: 0;

    @include breakpoint($large) {
      display: none;
    }
  }
}

.author__urls {
  display: none;
  position: absolute;
  right: 0;
  margin-top: 15px;
  padding: 10px;
  list-style-type: none;
  border: 1px solid $border-color;
  border-radius: $border-radius;
  background: #fff;
  z-index: -1;
  box-shadow: 0 0 10px rgba(#000, 0.25);
  cursor: default;

  @include breakpoint($large) {
    display: block;
    position: relative;
    margin: 0;
    padding: 0;
    border: 0;
    background: transparent;
    box-shadow: none;
  }

  &:before {
    display: block;
    content: "";
    position: absolute;
    top: -11px;
    left: calc(50% - 10px);
    width: 0;
    border-style: solid;
    border-width: 0 10px 10px;
    border-color: $border-color transparent;
    z-index: 0;

    @include breakpoint($large) {
      display: none;
    }
  }

  &:after {
    display: block;
    content: "";
    position: absolute;
    top: -10px;
    left: calc(50% - 10px);
    width: 0;
    border-style: solid;
    border-width: 0 10px 10px;
    border-color: #fff transparent;
    z-index: 1;

    @include breakpoint($large) {
      display: none;
    }
  }

  li {
    white-space: nowrap;
  }

    a {
    display: block;
    margin-bottom: 5px;
    padding: 4px 8px;
    color: inherit;
    font-size: 0.875em; /* Previously $type-size-6 */
    text-decoration: none;
    border-radius: $border-radius;
    transition: all 0.2s ease-in-out;

    &:hover {
      text-decoration: none;
      background-color: $lighter-gray;
    }
  }
}
