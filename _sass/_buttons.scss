/* ==========================================================================
   BUTTONS
   ========================================================================== */

/*
   Default button
   ========================================================================== */

.btn {
  /* Modern button design */
  display: inline-flex;
  align-items: center;
  justify-content: center;
  margin-bottom: $space-2;
  padding: $space-3 $space-6;
  color: #fff !important;
  font-family: $sans-serif;
  font-size: $type-size-7;
  font-weight: $font-weight-medium;
  text-align: center;
  text-decoration: none;
  background: $bg-gradient-accent;
  border: 0 !important;
  border-radius: $border-radius;
  cursor: pointer;
  transition: all $duration-normal $ease-out-cubic;
  box-shadow: $box-shadow-sm;
  position: relative;
  overflow: hidden;

  &:hover {
    transform: translateY(-2px) scale(1.02);
    box-shadow: $box-shadow-lg;

    &::before {
      opacity: 1;
    }
  }

  &::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(135deg, rgba(255,255,255,0.2) 0%, rgba(255,255,255,0.1) 100%);
    opacity: 0;
    transition: opacity $duration-normal $ease-out-cubic;
  }

  &:active {
    transform: translateY(0) scale(0.98);
    transition-duration: $duration-fast;
  }

  .icon {
    margin-right: 0.5em;
  }

  .icon + .hidden {
    margin-left: -0.5em; /* override for hidden text*/
  }

  /* fills width of parent container */

  &--block {
    display: block;
    width: 100%;

    + .btn--block {
      margin-top: 0.25em;
    }
  }

  /* Secondary button variant */
  &--secondary {
    background: linear-gradient(135deg, $secondary-color 0%, $accent-color 100%);

    &:hover {
      background: linear-gradient(135deg, darken($secondary-color, 10%) 0%, darken($accent-color, 10%) 100%);
    }
  }

  /* Outline button variant */
  &--outline {
    background: transparent;
    color: $primary-color !important;
    border: 2px solid $primary-color !important;

    &:hover {
      background: $primary-color;
      color: white !important;
      border-color: $primary-color !important;
    }
  }

  /* for dark backgrounds */
  &--inverse {
    color: $gray-600 !important;
    border: 2px solid $gray-300 !important;
    background: rgba(255, 255, 255, 0.9);
    backdrop-filter: blur(10px);

    &:hover {
      color: $primary-color !important;
      border-color: $primary-color !important;
      background: rgba(255, 255, 255, 1);
    }
  }

  /* light outline */

  &--light-outline {
    border: 1px solid #fff !important; /* override*/
    background-color: transparent;
  }

  /* information */

  &--info {
    background-color: $info-color;

    &:hover {
      background-color: mix(#000, $info-color, 15%);
    }
  }

  /* warning */

  &--warning {
    background-color: $warning-color;

    &:hover {
      background-color: mix(#000, $warning-color, 15%);
    }
  }

  /* success */

  &--success {
    background-color: $success-color;

    &:hover {
      background-color: mix(#000, $success-color, 15%);
    }
  }

  /* danger */

  &--danger {
    background-color: $danger-color;

    &:hover {
      background-color: mix(#000, $danger-color, 15%);
    }
  }

  /* disabled */

  &--disabled {
    pointer-events: none;
    cursor: not-allowed;
    filter: alpha(opacity=65);
    box-shadow: none;
    opacity: 0.65;
  }

  /* social buttons */

  $social:
  (facebook, $facebook-color),
  (twitter, $twitter-color),
  (google-plus, $google-plus-color),
  (linkedin, $linkedin-color);

  @each $socialnetwork, $color in $social {
    &--#{$socialnetwork} {
      background-color: $color;

      &:hover {
        background-color: mix(#000, $color, 15%);
      }
    }
  }

  /* extra large button */

  &--x-large {
    font-size: $type-size-4;
  }

  /* large button */

  &--large {
    font-size: $type-size-5;
  }

  /* small button */

  &--small {
    font-size: $type-size-7;
  }
}