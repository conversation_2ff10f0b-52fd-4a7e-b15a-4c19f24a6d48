/* ==========================================================================
   BUTTONS
   ========================================================================== */

/*
   Default button
   ========================================================================== */

.btn {
  /* default button */
  display: inline-block;
  margin-bottom: 0.25em;
  padding: 0.5em 1em;
  color: #fff !important;
  font-family: $sans-serif;
  font-size: $type-size-6;
  font-weight: bold;
  text-align: center;
  text-decoration: none;
  background-color: $primary-color;
  border: 0 !important;
  border-radius: $border-radius;
  cursor: pointer;
  transition: all 0.2s ease-in-out;

  &:hover {
    background-color: mix(#000, $primary-color, 15%);
    box-shadow: 0 4px 8px rgba(0,0,0,0.15);
    transform: translateY(-2px);
  }

  .icon {
    margin-right: 0.5em;
  }

  .icon + .hidden {
    margin-left: -0.5em; /* override for hidden text*/
  }

  /* fills width of parent container */

  &--block {
    display: block;
    width: 100%;

    + .btn--block {
      margin-top: 0.25em;
    }
  }

  /* for dark backgrounds */

  &--inverse {
    color: $gray !important;
    border: 1px solid $light-gray !important; /* override*/
    background-color: #fff;

    &:hover {
      color: #fff !important;
      border-color: $gray;
    }
  }

  /* light outline */

  &--light-outline {
    border: 1px solid #fff !important; /* override*/
    background-color: transparent;
  }

  /* information */

  &--info {
    background-color: $info-color;

    &:hover {
      background-color: mix(#000, $info-color, 15%);
    }
  }

  /* warning */

  &--warning {
    background-color: $warning-color;

    &:hover {
      background-color: mix(#000, $warning-color, 15%);
    }
  }

  /* success */

  &--success {
    background-color: $success-color;

    &:hover {
      background-color: mix(#000, $success-color, 15%);
    }
  }

  /* danger */

  &--danger {
    background-color: $danger-color;

    &:hover {
      background-color: mix(#000, $danger-color, 15%);
    }
  }

  /* disabled */

  &--disabled {
    pointer-events: none;
    cursor: not-allowed;
    filter: alpha(opacity=65);
    box-shadow: none;
    opacity: 0.65;
  }

  /* social buttons */

  $social:
  (facebook, $facebook-color),
  (twitter, $twitter-color),
  (google-plus, $google-plus-color),
  (linkedin, $linkedin-color);

  @each $socialnetwork, $color in $social {
    &--#{$socialnetwork} {
      background-color: $color;

      &:hover {
        background-color: mix(#000, $color, 15%);
      }
    }
  }

  /* extra large button */

  &--x-large {
    font-size: $type-size-4;
  }

  /* large button */

  &--large {
    font-size: $type-size-5;
  }

  /* small button */

  &--small {
    font-size: $type-size-7;
  }
}