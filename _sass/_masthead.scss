/* ==========================================================================
   MASTHEAD
   ========================================================================== */

.masthead {
  position: fixed;
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(20px);
  -webkit-backdrop-filter: blur(20px);
  border-bottom: 1px solid rgba(229, 231, 235, 0.8);
  height: $masthead-height;
  top: 0;
  width: 100%;
  box-shadow: $box-shadow-sm;

  -webkit-animation: intro 0.4s both $ease-out-cubic;
          animation: intro 0.4s both $ease-out-cubic;
  -webkit-animation-delay: 0.1s;
          animation-delay: 0.1s;
  z-index: 20;

  &__inner-wrap {
    @include container;
    @include clearfix;
    padding: 1em 1em 1em;
    font-family: $sans-serif-narrow;

    @include breakpoint($x-large) {
      max-width: $x-large;
    }

    nav {
      z-index: 10;
    }

    a {
      text-decoration: none;
    }
  }
}

.masthead__menu {

  ul {
    margin: 0;
    padding: 0;
    clear: both;
    list-style-type: none;
  }
}

.masthead__menu-item {
  display: block;
  list-style-type: none;
  white-space: nowrap;

  &--lg {
    padding-right: 2em;
    font-weight: 700;
  }
}