{% if post.read_time %}
  {% assign words = post.content | strip_html | number_of_words %}
{% elsif page.read_time %}
  {% assign words = page.content | strip_html | number_of_words %}
{% endif %}

{% if site.words_per_minute %}
  {% if words < 180 %}
	  {{ site.data.ui-text[site.locale].less_than | default: "less than" }} 1 {{ site.data.ui-text[site.locale].minute_read | default: "minute read" }}
	{% elsif words < 360 %}
	  1 {{ site.data.ui-text[site.locale].minute_read | default: "minute read" }}
	{% else %}
	  {{ words | divided_by:site.words_per_minute }} {{ site.data.ui-text[site.locale].minute_read | default: "minute read" }}
	{% endif %}
{% else %}
  {{ site.data.ui-text[site.locale].undefined_wpm | "Undefined parameter words_per_minute at _config.yml" }}
{% endif %}