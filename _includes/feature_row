{% include base_path %}

{% if include.id %}
  {% assign feature_row = page.[include.id] %}
{% else %}
  {% assign feature_row = page.feature_row %}
{% endif %}

<div class="feature__wrapper">

  {% for f in feature_row %}

    {% if f.url contains "://" %}
      {% capture f_url %}{{ f.url }}{% endcapture %}
    {% else %}
      {% capture f_url %}{{ f.url | prepend: base_path }}{% endcapture %}
    {% endif %}

    <div class="feature__item{% if include.type %}--{{ include.type }}{% endif %}">
      <div class="archive__item">
        {% if f.image_path %}
          <div class="archive__item-teaser">
            <img src=
              {% if f.image_path contains "://" %}
                "{{ f.image_path }}"
              {% else %}
                "{{ f.image_path | prepend: "/images/" | prepend: base_path }}"
              {% endif %}
            alt="{% if f.alt %}{{ f.alt }}{% endif %}">
          </div>
        {% endif %}

        <div class="archive__item-body">
          {% if f.title %}
            <h2 class="archive__item-title">{{ f.title }}</h2>
          {% endif %}

          {% if f.excerpt %}
            <div class="archive__item-excerpt">
              {{ f.excerpt | markdownify }}
            </div>
          {% endif %}

          {% if f.url %}
            <p><a href="{{ f_url }}" class="btn {{ f.btn_class }}">{{ f.btn_label | default: site.data.ui-text[site.locale].more_label | default: "Learn More" }}</a></p>
          {% endif %}
        </div>
      </div>
    </div>
  {% endfor %}

</div>