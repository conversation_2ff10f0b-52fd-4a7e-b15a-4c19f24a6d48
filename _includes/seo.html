{% include base_path %}

<!-- begin SEO -->
{% if site.url %}
  {% assign seo_url = site.url | append: site.baseurl %}
{% endif %}
{% assign seo_url = seo_url | default: site.github.url %}

{% if page.title %}
  {% assign seo_title = page.title | append: " " | append: site.title_separator | append: " " | append: site.title %}
{% endif %}

{% if seo_title %}
  {% assign seo_title = seo_title | markdownify | strip_html | strip_newlines | escape_once %}
{% endif %}

{% if site.url %}
  {% assign canonical_url = page.url | replace: "index.html", "" | prepend: site.url %}
{% endif %}

<title>{{ seo_title | default: site.title }}{% if paginator %}{% unless paginator.page == 1 %} {{ site.title_separator }} {{ site.data.ui-text[site.locale].page | default: "Page" }} {{ paginator.page }}{% endunless %}{% endif %}</title>

{% assign seo_description = page.description | default: page.excerpt | default: site.description %}
{% if seo_description %}
  {% assign seo_description = seo_description | markdownify | strip_html | strip_newlines | escape_once %}
{% endif %}

{% assign seo_author = page.author | default: page.author[0] | default: site.author[0] %}
{% if seo_author %}
  {% if seo_author.twitter %}
    {% assign seo_author_twitter = seo_author.twitter %}
  {% else %}
    {% if site.data.authors and site.data.authors[seo_author] %}
      {% assign seo_author_twitter = site.data.authors[seo_author].twitter  %}
    {% else %}
      {% assign seo_author_twitter = seo_author  %}
    {% endif %}
  {% endif %}
  {% assign seo_author_twitter = seo_author_twitter | replace: "@", "" %}
{% endif %}

<meta property="og:locale" content="{{ site.locale | default: "en" }}">
<meta property="og:site_name" content="{{ site.title }}">
<meta property="og:title" content="{{ page.title | default: site.title | markdownify | strip_html | strip_newlines | escape_once }}">

{% if seo_url %}
  <link rel="canonical" href="{{ page.url | prepend: seo_url | replace: "/index.html", "/" }}">
  <meta property="og:url" content="{{ page.url | prepend: seo_url | replace: "/index.html", "/" }}">
{% endif %}

{% if page.excerpt %}
  <meta property="og:description" name="description" content="{{ seo_description }}">
{% endif %}

{% if site.twitter.username %}
  <meta name="twitter:site" content="@{{ site.twitter.username | replace: "@", "" }}">
  <meta name="twitter:title" content="{{ page.title | default: site.title | markdownify | strip_html | strip_newlines | escape_once }}">
  <meta name="twitter:description" content="{{ seo_description }}">
  <meta name="twitter:url" content="{{ canonical_url }}">

  {% if page.header.image %}
    <meta name="twitter:card" content="summary_large_image">
    <meta name="twitter:image" content="{% if page.header.image contains "://" %}{{ page.header.image }}{% else %}{{ page.header.image | prepend: "/images/" | prepend: base_path }}{% endif %}">
  {% else %}
    <meta name="twitter:card" content="summary">
    {% if site.og_image %}
      <meta name="twitter:image" content="{{ site.og_image | prepend: "/images/" | prepend: base_path }}">
    {% endif %}
  {% endif %}

  {% if seo_author_twitter %}
    <meta name="twitter:creator" content="@{{ seo_author_twitter }}">
  {% endif %}
{% endif %}

{% if site.facebook %}
  {% if site.facebook.publisher %}
    <meta property="article:publisher" content="{{ site.facebook.publisher }}">
  {% endif %}

  {% if site.facebook.app_id %}
    <meta property="fb:app_id" content="{{ site.facebook.app_id }}">
  {% endif %}
{% endif %}

{% if page.header.image %}
  <meta property="og:image" content="{% if page.header.image contains "://" %}{{ page.header.image }}{% else %}{{ page.header.image | prepend: "/images/" | prepend: base_path }}{% endif %}">
{% elsif page.header.overlay_image %}
  <meta property="og:image" content="{% if page.header.overlay_image contains "://" %}{{ page.header.overlay_image }}{% else %}{{ page.header.overlay_image | prepend: "/images/" | prepend: base_path }}{% endif %}">
{% endif %}

{% if page.date %}
  <meta property="og:type" content="article">
  <meta property="article:published_time" content="{{ page.date | date_to_xmlschema }}">
{% endif %}

{% if paginator.previous_page %}
  <link rel="prev" href="{{ paginator.previous_page_path | prepend: seo_url }}">
{% endif %}
{% if paginator.next_page %}
  <link rel="next" href="{{ paginator.next_page_path | prepend: seo_url }}">
{% endif %}

{% if site.og_image %}
  <script type="application/ld+json">
    {
      "@context": "http://schema.org",
      "@type": "Organization",
      "url": {{ seo_url | jsonify }},
      "logo": {{ site.og_image | prepend: "/images/" | prepend: base_path | jsonify }}
    }
  </script>
{% endif %}

{% if site.social %}
  <script type="application/ld+json">
    {
      "@context" : "http://schema.org",
      "@type" : "{% if site.social.type %}{{ site.social.type }}{% else %}Person{% endif %}",
      "name" : "{{ site.social.name | default: site.name }}",
      "url" : {{ seo_url | jsonify }},
      "sameAs" : {{ site.social.links | jsonify }}
    }
  </script>
{% endif %}

{% if site.google_site_verification %}
  <meta name="google-site-verification" content="{{ site.google_site_verification }}" />
{% endif %}
{% if site.bing_site_verification %}
  <meta name="msvalidate.01" content="{{ site.bing_site_verification }}">
{% endif %}
{% if site.alexa_site_verification %}
  <meta name="alexaVerifyID" content="{{ site.alexa_site_verification }}">
{% endif %}
{% if site.yandex_site_verification %}
  <meta name="yandex-verification" content="{{ site.yandex_site_verification }}">
{% endif %}
<!-- end SEO -->
