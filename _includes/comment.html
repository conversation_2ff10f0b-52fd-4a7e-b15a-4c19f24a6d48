<article id="comment{{ include.index }}" class="js-comment comment">
  <div class="comment__avatar-wrapper">
    <img class="comment__avatar" src="https://www.gravatar.com/avatar/{{ include.email }}?d=mm&s=80">
  </div>
  <div class="comment__content-wrapper">
    <h3 class="comment__author">
      {% unless include.url == blank %}
        <a rel="nofollow" href="{{ include.url }}">{{ include.name }}</a>
      {% else %}
        {{ include.name }}
      {% endunless %}
    </h3>
    <p class="comment__date">
      {% if include.date %}
        {% if include.index %}<a href="#comment{{ include.index }}">{% endif %}
        <time datetime="{{ include.date | date_to_xmlschema }}">{{ include.date | date: "%B %d, %Y at %I:%M %p" }}</time>
        {% if include.index %}</a>{% endif %}
      {% endif %}
    </p>
    {{ include.message | markdownify }}
  </div>
</article>