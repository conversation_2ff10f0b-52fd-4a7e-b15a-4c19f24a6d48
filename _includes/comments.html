{% include base_path %}

<div class="page__comments">
  {% capture comments_label %}{{ site.data.ui-text[site.locale].comments_label | default: "Comments" }}{% endcapture %}
  {% case site.comments.provider %}
  {% when "disqus" %}
    <h4 class="page__comments-title">{{ comments_label }}</h4>
    <section id="disqus_thread"></section>
  {% when "facebook" %}
    <h4 class="page__comments-title">{{ comments_label }}</h4>
    <section class="fb-comments" data-href="{{ base_path }}{{ page.url }}" data-mobile="true" data-num-posts="{{ site.comments.facebook.num_posts | default: 5 }}" data-width="100%" data-colorscheme="{{ site.comments.facebook.colorscheme | default: 'light' }}"></section>
  {% when "google-plus" %}
    <h4 class="page__comments-title">{{ comments_label }}</h4>
    <section class="g-comments" data-href="{{ base_path }}{{ page.url }}" data-first_party_property="BLOGGER" data-view_type="FILTERED_POSTMOD">Loading Google+ Comments ...</section>
  {% when "staticman" %}
    <section id="comments">
      {% if site.repository and site.staticman.branch %}
        <!-- Start static comments -->
        <div class="js-comments">
          {% if site.data.comments[page.slug] %}
            <h4 class="page__comments-title">{{ site.data.ui-text[site.locale].comments_title | default: "Comments" }}</h4>
            {% assign comments = site.data.comments[page.slug] | sort %}

            {% for comment in comments %}
              {% assign email = comment[1].email %}
              {% assign name = comment[1].name %}
              {% assign url = comment[1].url %}
              {% assign date = comment[1].date %}
              {% assign message = comment[1].message %}
              {% include comment.html index=forloop.index email=email name=name url=url date=date message=message %}
            {% endfor %}
          {% endif %}
        </div>
        <!-- End static comments -->

        <!-- Start new comment form -->
        <h4 class="page__comments-title">{{ site.data.ui-text[site.locale].comments_label | default: "Leave a Comment" }}</h4>
        <p class="small">{{ site.data.ui-text[site.locale].comment_form_info | default: "Your email address will not be published. Required fields are marked" }} <span class="required">*</span></p>
        <form id="new_comment" class="page__comments-form js-form form" method="post" action="https://api.staticman.net/v1/entry/{{ site.repository }}/{{ site.staticman.branch }}">
          <div class="form__spinner">
            <i class="fa fa-spinner fa-spin fa-3x fa-fw"></i>
            <span class="sr-only">{{ site.data.ui-text[site.locale].loading_label | default: "Loading..." }}</span>
          </div>

          <fieldset>
            <label for="comment-form-message">{{ site.data.ui-text[site.locale].comment_form_comment_label | default: "Comment" }} <small class="required">*</small></label>
            <textarea type="text" rows="3" id="comment-form-message" name="fields[message]" tabindex="1"></textarea>
            <div class="small help-block"><a href="https://daringfireball.net/projects/markdown/">{{ site.data.ui-text[site.locale].comment_form_md_info | default: "Markdown is supported." }}</a></div>
          </fieldset>
          <fieldset>
            <label for="comment-form-name">{{ site.data.ui-text[site.locale].comment_form_name_label | default: "Name" }} <small class="required">*</small></label>
            <input type="text" id="comment-form-name" name="fields[name]" tabindex="2" />
          </fieldset>
          <fieldset>
            <label for="comment-form-email">{{ site.data.ui-text[site.locale].comment_form_email_label | default: "Email address" }} <small class="required">*</small></label>
            <input type="email" id="comment-form-email" name="fields[email]" tabindex="3" />
          </fieldset>
          <fieldset>
            <label for="comment-form-url">{{ site.data.ui-text[site.locale].comment_form_website_label | default: "Website (optional)" }}</label>
            <input type="url" id="comment-form-url" name="fields[url]" tabindex="4"/>
          </fieldset>
          <fieldset class="hidden">
            <input type="hidden" name="options[slug]" value="{{ page.slug }}">
            <input type="hidden" name="fields[hidden]"/>
          </fieldset>
          <!-- Start comment form alert messaging -->
          <p class="hidden js-notice">
            <strong class="js-notice-text"></strong>
          </p>
          <!-- End comment form alert messaging -->
          <fieldset>
            <button type="submit" id="comment-form-submit" tabindex="5" class="btn btn--large">{{ site.data.ui-text[site.locale].comment_btn_submit | default: "Submit Comment" }}</button>
          </fieldset>
        </form>
        <!-- End new comment form -->
      {% endif %}
    </section>
  {% when "custom" %}
    <h4 class="page__comments-title">{{ comments_label }}</h4>
    <section id="comments"></section>
    {% include /comments-providers/scripts.html %}
  {% endcase %}
</div>