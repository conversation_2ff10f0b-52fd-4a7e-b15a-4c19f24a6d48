{% include base_path %}
{% assign navigation = site.data.navigation[include.nav] %}

<nav class="nav__list">
  {% if page.sidebar.title %}<header><h4 class="nav__title" style="padding: 0;">{{ page.sidebar.title }}</h4></header>{% endif %}
  <ul>
    {% for nav in navigation %}
      <li>
        {% if nav.url %}
          {% comment %}internal/external URL check{% endcomment %}
          {% if nav.url contains "://" %}
            {% assign domain = "" %}
          {% else %}
            {% assign domain = base_path %}
          {% endif %}

          <a href="{{ domain }}{{ nav.url }}"><span class="nav__sub-title">{{ nav.title }}</span></a>
        {% else %}
          <span class="nav__sub-title">{{ nav.title }}</span>
        {% endif %}

        {% if nav.children != null %}
        <ul>
          {% for child in nav.children %}
            {% comment %}internal/external URL check{% endcomment %}
            {% if child.url contains "://" %}
              {% assign domain = "" %}
            {% else %}
              {% assign domain = base_path %}
            {% endif %}

            {% comment %}set "active" class on current page{% endcomment %}
            {% if child.url == page.url %}
              {% assign active = "active" %}
            {% else %}
              {% assign active = "" %}
            {% endif %}

            <li><a href="{{ domain }}{{ child.url }}" class="{{ active }}">{{ child.title }}</a></li>
          {% endfor %}
        </ul>
        {% endif %}
      </li>
    {% endfor %}
  </ul>
</nav>