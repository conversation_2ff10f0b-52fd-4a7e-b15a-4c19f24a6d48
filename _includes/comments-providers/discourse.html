{% if site.comments.discourse.server %}
{% include base_path %}
{% capture canonical %}{{ base_path }}{% if site.permalink contains '.html' %}{{ page.url }}{% else %}{{ page.url | remove:'index.html' | strip_slash }}{% endif %}{% endcapture %}
<script type="text/javascript">
    DiscourseEmbed = { discourseUrl: '//{{ site.comments.discourse.server }}/',
                       discourseEmbedUrl: '{{ canonical }}' };
   (function () {
     var d = document.createElement('script'); d.type = 'text/javascript'; d.async = true;
     d.src = DiscourseEmbed.discourseUrl + 'javascripts/embed.js';
     (document.getElementsByTagName('head')[0] || document.getElementsByTagName('body')[0]).appendChild(d);
   })();
</script>
<noscript>Please enable JavaScript to view the comments powered by [Discourse](http://forum.beta-europe.org/c/beta/website).</a></noscript>
{% endif %}