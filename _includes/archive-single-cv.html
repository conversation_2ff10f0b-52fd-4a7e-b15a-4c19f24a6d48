{% include base_path %}

{% if post.header.teaser %}
  {% capture teaser %}{{ post.header.teaser }}{% endcapture %}
{% else %}
  {% assign teaser = site.teaser %}
{% endif %}

{% if post.id %}
  {% assign title = post.title | markdownify | remove: "<p>" | remove: "</p>" %}
{% else %}
  {% assign title = post.title %}
{% endif %}

<div class="{{ include.type | default: "list" }}__item">
  <article class="archive__item" itemscope itemtype="http://schema.org/CreativeWork">
    <li>
    {% if include.type == "grid" and teaser %}
      <div class="archive__item-teaser">
        <img src=
          {% if teaser contains "://" %}
            "{{ teaser }}"
          {% else %}
            "{{ teaser | prepend: "/images/" | prepend: base_path }}"
          {% endif %}
          alt="">
      </div>
    {% endif %}
    <h3 class="archive__item-title" itemprop="headline">
      {% if post.link %}
        <a href="{{ post.link }}">{{ title }}</a> <a href="{{ base_path }}{{ post.url }}" rel="permalink"><i class="fa fa-link" aria-hidden="true" title="permalink"></i><span class="sr-only">Permalink</span></a>
      {% else %}
        <a href="{{ base_path }}{{ post.url }}" rel="permalink">{{ title }}</a>
      {% endif %}
    </h3>
    {% if post.read_time %}
      <p class="page__meta"><i class="fa fa-clock-o" aria-hidden="true"></i> {% include read-time.html %}</p>
    {% endif %}
    {% if post.collection == 'teaching' %}
      {% comment %} For teaching in CV, show course information in blue box {% endcomment %}
      <div class="archive__item-excerpt" itemprop="description">
        <strong>Course Information:</strong><br>
        {{ post.type }}{% if post.venue %}, <i>{{ post.venue }}</i>{% endif %}{% if post.date %}, {{ post.date | default: "1900-01-01" | date: "%Y" }}{% endif %}
        {% if post.location %}<br><i class="fa fa-map-marker" aria-hidden="true"></i> {{ post.location }}{% endif %}
      </div>
    {% elsif post.venue %}
      {% comment %} For publications and other collections {% endcomment %}
      <p class="archive__item-excerpt" itemprop="description">{{ post.citation }}</p>
    {% endif %}
    </li>
 </article>
</div>
