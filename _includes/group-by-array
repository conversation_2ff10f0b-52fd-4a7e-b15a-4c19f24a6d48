<!--
# Jekyll Group-By-Array 0.1.0
# https://github.com/mushishi78/jekyll-group-by-array
# <AUTHOR> <EMAIL>
# MIT License
-->

<!-- Initialize -->
{% assign __empty_array = '' | split: ',' %}
{% assign group_names = __empty_array %}
{% assign group_items = __empty_array %}

<!-- Map -->
{% assign __names =  include.collection | map: include.field %}

<!-- Flatten -->
{% assign __names =  __names | join: ',' | join: ',' | split: ',' %}

<!-- Uniq -->
{% assign __names =  __names | sort %}
{% for name in __names | sort %}

<!-- If not equal to previous then it must be unique as sorted -->
{% unless name == previous %}

<!-- Push to group_names -->
{% assign group_names = group_names | push: name %}
{% endunless %}

{% assign previous = name %}
{% endfor %}


<!-- group_items -->
{% for name in group_names %}

<!-- Collect if contains -->
{% assign __item = __empty_array %}
{% for __element in include.collection %}
{% if __element[include.field] contains name %}
{% assign __item = __item | push: __element %}
{% endif %}
{% endfor %}

<!-- Push to group_items -->
{% assign group_items = group_items | push: __item %}
{% endfor %}