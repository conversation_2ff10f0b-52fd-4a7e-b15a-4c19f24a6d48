{% include base_path %}

{% if include.id %}
  {% assign gallery = page.[include.id] %}
{% else %}
  {% assign gallery = page.gallery %}
{% endif %}

{% if gallery.size == 2 %}
  {% assign gallery_layout = 'half' %}
{% elsif gallery.size >= 3 %}
  {% assign gallery_layout = 'third' %}
{% else %}
  {% assign gallery_layout = '' %}
{% endif %}

<figure class="{{ gallery_layout }} {{ include.class }}">
  {% for img in gallery %}
    {% if img.url %}
      <a href=
        {% if img.url contains "://" %}
          "{{ img.url }}"
        {% else %}
          "{{ img.url | prepend: "/images/" | prepend: base_path }}"
        {% endif %}
        {% if img.title %}title="{{ img.title }}"{% endif %}
      >
        <img src=
          {% if img.image_path contains "://" %}
            "{{ img.image_path }}"
          {% else %}
            "{{ img.image_path | prepend: "/images/" | prepend: base_path }}"
          {% endif %}
          alt="{% if img.alt %}{{ img.alt }}{% endif %}">
      </a>
    {% else %}
      <img src=
        {% if img.image_path contains "://" %}
          "{{ img.image_path }}"
        {% else %}
          "{{ img.image_path | prepend: "/images/" | prepend: base_path }}"
        {% endif %}
        alt="{% if img.alt %}{{ img.alt }}{% endif %}">
    {% endif %}
  {% endfor %}
  {% if include.caption %}
    <figcaption>{{ include.caption | markdownify | remove: "<p>" | remove: "</p>" }}</figcaption>
  {% endif %}
</figure>