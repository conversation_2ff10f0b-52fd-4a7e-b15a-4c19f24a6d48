{% include base_path %}

{% case site.categories.type %}
  {% when "liquid" %}
    {% assign path_type = "#" %}
  {% when "jekyll-archives" %}
    {% assign path_type = nil %}
{% endcase %}

{% if page.collection != 'posts' %}
  {% assign path_type = nil %}
  {% assign crumb_path = '/' %}
{% else %}
  {% assign crumb_path = site.categories.path %}
{% endif %}

<nav class="breadcrumbs">
  <ol itemscope itemtype="http://schema.org/BreadcrumbList">
    {% assign crumbs = page.url | split: '/' %}
    {% assign i = 1 %}
    {% for crumb in crumbs offset: 1 %}
      {% if forloop.first %}
        <li itemprop="itemListElement" itemscope itemtype="http://schema.org/ListItem">
          <a href="{{ base_path }}/" itemprop="item"><span itemprop="name">{{ site.data.ui-text[site.locale].breadcrumb_home_label | default: "Home" }}</span></a>
          <meta itemprop="position" content="{{ i }}" />
        </li>
        <span class="sep">{{ site.data.ui-text[site.locale].breadcrumb_separator | default: "/" }}</span>
      {% endif %}
      {% if forloop.last %}
        <li class="current">{{ page.title }}</li>
      {% else %}
        {% assign i = i | plus: 1 %}
        <li itemprop="itemListElement" itemscope itemtype="http://schema.org/ListItem">
          <a href="{{ base_path }}{{ crumb | downcase | replace: '%20', '-' | prepend: path_type | prepend: crumb_path }}" itemprop="item"><span itemprop="name">{{ crumb | replace: '-', ' ' | replace: '%20', ' ' | capitalize }}</span></a>
          <meta itemprop="position" content="{{ i }}" />
        </li>
        <span class="sep">{{ site.data.ui-text[site.locale].breadcrumb_separator | default: "/" }}</span>
      {% endif %}
    {% endfor %}
  </ol>
</nav>