{% include base_path %}

{% case site.tag_archive.type %}
  {% when "liquid" %}
    {% assign path_type = "#" %}
  {% when "jekyll-archives" %}
    {% assign path_type = nil %}
{% endcase %}

{% if site.tag_archive.path %}
  {% comment %}
    <!-- Sort alphabetically regardless of case e.g. a B c d E -->
    <!-- modified from http://www.codeofclimber.ru/2015/sorting-site-tags-in-jekyll/ -->
  {% endcomment %}
  {% capture page_tags %}{% for tag in page.tags %}{{ tag | downcase }}#{{ tag }}{% unless forloop.last %},{% endunless %}{% endfor %}{% endcapture %}
  {% assign tag_hashes = (page_tags | split: ',' | sort:0) %}

  <p class="page__taxonomy">
    <strong><i class="fa fa-fw fa-tags" aria-hidden="true"></i> {{ site.data.ui-text[site.locale].tags_label | default: "Tags:" }} </strong>
    <span itemprop="keywords">
    {% for hash in tag_hashes %}
      {% assign keyValue = hash | split: '#' %}
      {% capture tag_word %}{{ keyValue[1] | strip_newlines }}{% endcapture %}
      <a href="{{ base_path }}{{ tag_word | slugify | prepend: path_type | prepend: site.tag_archive.path }}" class="page__taxonomy-item" rel="tag">{{ tag_word }}</a>{% unless forloop.last %}<span class="sep">, </span>{% endunless %}
    {% endfor %}
    </span>
  </p>
{% endif %}